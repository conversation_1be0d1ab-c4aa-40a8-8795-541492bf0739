<?php
// Start session
session_start();

// Check if the user is authenticated
if (!isset($_SESSION['auth_user']) || empty($_SESSION['auth_user']['id'])) {
    // User is not logged in, redirect to login page
    header('Location: login.php');
    exit; // Make sure to exit to prevent further script execution
}

// Include database connection
require_once 'database/conn.php';

// Create a simple database wrapper class for this file
class SimpleDB {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function fetchRow($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }

    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($data);

        return $this->pdo->lastInsertId();
    }
}

// Create database instance
$db = new SimpleDB($pdo);

// Initialize variables
$success_message = '';
$error_message = '';
$form_data = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate and sanitize input data
        $name = trim(htmlspecialchars($_POST['name'] ?? '', ENT_QUOTES, 'UTF-8'));
        $phone = trim(htmlspecialchars($_POST['phone'] ?? '', ENT_QUOTES, 'UTF-8'));
        $email = filter_var($_POST['email'] ?? '', FILTER_SANITIZE_EMAIL);
        $user_role = trim(htmlspecialchars($_POST['user_role'] ?? '', ENT_QUOTES, 'UTF-8'));
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $address = trim(htmlspecialchars($_POST['address'] ?? '', ENT_QUOTES, 'UTF-8'));

        // Store form data for repopulation on error
        $form_data = [
            'name' => $name,
            'phone' => $phone,
            'email' => $email,
            'user_role' => $user_role,
            'address' => $address
        ];

        // Validation
        $errors = [];

        if (empty($name)) $errors[] = "Full name is required";
        if (empty($phone)) $errors[] = "Phone number is required";
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = "Valid email is required";
        if (empty($user_role)) $errors[] = "User role is required";
        if (empty($password)) $errors[] = "Password is required";
        if (strlen($password) < 8) $errors[] = "Password must be at least 8 characters";
        if ($password !== $confirm_password) $errors[] = "Passwords do not match";

        // Check if email already exists
        $existing_user = $db->fetchRow("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existing_user) {
            $errors[] = "Email address already exists";
        }

        // Handle file upload - Store only file path in database
        $profile_image_path = null;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/profiles/';

            // Create upload directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            // Validate file type
            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = "Only JPG, JPEG, PNG, GIF, and WebP files are allowed";
            }
            // Validate file size (2MB limit)
            elseif ($_FILES['profile_image']['size'] > 2 * 1024 * 1024) {
                $errors[] = "File size must be less than 2MB";
            }
            // Validate actual image file (security check)
            elseif (!getimagesize($_FILES['profile_image']['tmp_name'])) {
                $errors[] = "Invalid image file";
            }
            else {
                // Generate unique filename to prevent conflicts
                $unique_filename = uniqid('profile_', true) . '.' . $file_extension;
                $upload_path = $upload_dir . $unique_filename;

                // Move uploaded file to destination
                if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                    // Store only the relative path in database (not the full system path)
                    $profile_image_path = $upload_path;

                    // Optional: Resize image to save space (uncomment if needed)
                    // resizeImage($upload_path, 300, 300);
                } else {
                    $errors[] = "Failed to upload profile image";
                }
            }
        }

        // If no errors, insert user
        if (empty($errors)) {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            $user_data = [
                'name' => $name,
                'phone' => $phone,
                'email' => $email,
                'user_role' => $user_role,
                'password' => $hashed_password,
                'address' => $address,
                'profile_image' => $profile_image_path  // Store file path, not file data
            ];

            $user_id = $db->insert('users', $user_data);

            if ($user_id) {
                $success_message = "User created successfully! User ID: " . $user_id;
                if ($profile_image_path) {
                    $success_message .= "<br><strong>Profile image uploaded:</strong> " . htmlspecialchars($profile_image_path);
                }
                $form_data = []; // Clear form data on success

                // Don't automatically log in the created user
                // The admin who creates the user should remain logged in

                // Log the activity (optional - only if table exists)
                try {
                    $db->insert('user_activity_log', [
                        'user_id' => $user_id,
                        'action' => 'user_created',
                        'description' => "New user created: {$name} ({$email})",
                        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                    ]);
                } catch (Exception $e) {
                    // Ignore if activity log table doesn't exist
                    error_log("Activity log failed: " . $e->getMessage());
                }
            } else {
                $error_message = "Failed to create user. Please try again.";
            }
        } else {
            $error_message = implode('<br>', $errors);
        }

    } catch (Exception $e) {
        error_log("User creation error: " . $e->getMessage());
        $error_message = "An error occurred while creating the user. Please try again.";
    }
}
?>
<?php include 'include/header.php'; ?>
    <!-- Preloader -->
    
    <!-- Main Wrapper -->
    <div id="main-wrapper" class="d-flex">
        <?php include 'include/sidebar.php'; ?>
       <!-- Content Wrapper -->
        <div class="content-wrapper">
            <?php include 'include/top-navbar.php'; ?>

            <!-- Main Content -->
            <div class="main-content">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex align-items-lg-center flex-column flex-md-row flex-lg-row mt-3">
                            <div class="flex-grow-1">
                                <h3 class="mb-2 text-color-2">Create New User</h3>
                                <p class="text-muted">Add a new user to the system with appropriate role and permissions</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12 col-lg-8">
                        <div class="card shadow-sm border-0">
                            <div class="card-body p-4">
                                <?php if (!empty($success_message)): ?>
                                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                                        <i class="fa-solid fa-check-circle me-2"></i>
                                        <?php echo $success_message; ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($error_message)): ?>
                                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                        <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                        <?php echo $error_message; ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                <?php endif; ?>

                                <form action="" method="post" enctype="multipart/form-data" class="row g-4">
                                    <!-- Name Field -->
                                    <div class="col-md-6">
                                        <label for="name" class="form-label text-color-2 fw-medium">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" placeholder="Enter full name" value="<?php echo htmlspecialchars($form_data['name'] ?? '', ENT_QUOTES); ?>" required>
                                    </div>

                                    <!-- Phone Field -->
                                    <div class="col-md-6">
                                        <label for="phone" class="form-label text-color-2 fw-medium">Phone Number <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="phone" name="phone" placeholder="Enter phone number" value="<?php echo htmlspecialchars($form_data['phone'] ?? '', ENT_QUOTES); ?>" required>
                                    </div>

                                    <!-- Email Field -->
                                    <div class="col-md-6">
                                        <label for="email" class="form-label text-color-2 fw-medium">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter email address" value="<?php echo htmlspecialchars($form_data['email'] ?? '', ENT_QUOTES); ?>" required>
                                    </div>

                                    <!-- User Role Field -->
                                    <div class="col-md-6">
                                        <label for="user_role" class="form-label text-color-2 fw-medium">User Role <span class="text-danger">*</span></label>
                                        <select class="form-select" id="user_role" name="user_role" required>
                                            <option value="" <?php echo empty($form_data['user_role']) ? 'selected' : ''; ?> disabled>Select a role</option>
                                            <option value="admin" <?php echo ($form_data['user_role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                            <option value="super_admin" <?php echo ($form_data['user_role'] ?? '') === 'super_admin' ? 'selected' : ''; ?>>Super Admin</option>
                                            <option value="author" <?php echo ($form_data['user_role'] ?? '') === 'author' ? 'selected' : ''; ?>>Author</option>
                                            <option value="editor" <?php echo ($form_data['user_role'] ?? '') === 'editor' ? 'selected' : ''; ?>>Editor</option>
                                            <option value="subscriber" <?php echo ($form_data['user_role'] ?? '') === 'subscriber' ? 'selected' : ''; ?>>Subscriber</option>
                                            <option value="student" <?php echo ($form_data['user_role'] ?? '') === 'student' ? 'selected' : ''; ?>>Student</option>
                                        </select>
                                    </div>

                                    <!-- Password Field -->
                                    <div class="col-md-6">
                                        <label for="password" class="form-label text-color-2 fw-medium">Password <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control" id="password" name="password" placeholder="Enter password" required>
                                    </div>

                                    <!-- Confirm Password Field -->
                                    <div class="col-md-6">
                                        <label for="confirm_password" class="form-label text-color-2 fw-medium">Confirm Password <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm password" required>
                                    </div>

                                    <!-- Address Field -->
                                    <div class="col-12">
                                        <label for="address" class="form-label text-color-2 fw-medium">Address</label>
                                        <textarea class="form-control" id="address" name="address" rows="3" placeholder="Enter full address"><?php echo htmlspecialchars($form_data['address'] ?? '', ENT_QUOTES); ?></textarea>
                                    </div>

                                    <!-- Profile Image Field -->
                                    <div class="col-12">
                                        <label for="profile_image" class="form-label text-color-2 fw-medium">Profile Image</label>
                                        <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                                        <small class="text-muted">
                                            <i class="fa-solid fa-info-circle me-1"></i>
                                            Upload a profile picture (optional).
                                            <strong>Supported formats:</strong> JPG, JPEG, PNG, GIF, WebP.
                                            <strong>Max size:</strong> 2MB.
                                            <br>
                                            <em>Note: Only the file path will be stored in the database, not the actual image data.</em>
                                        </small>
                                    </div>

                                    <!-- Submit Button -->
                                    <div class="col-12 mt-4">
                                        <button type="submit" class="btn bg-primary text-white px-4 py-2">
                                            <i class="fa-solid fa-user-plus me-2"></i> Create User
                                        </button>
                                        <button type="reset" class="btn btn-light border ms-2 px-4 py-2">
                                            <i class="fa-solid fa-rotate me-2"></i> Reset
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-lg-4">
                        <div class="card shadow-sm border-0">
                            <div class="card-header bg-white py-3">
                                <h5 class="card-title mb-0 text-color-2">User Information</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="mb-4">
                                    <h6 class="text-color-2 fw-medium">About User Roles</h6>
                                    <ul class="list-unstyled mt-3">
                                        <li class="mb-2"><span class="fw-medium">Super Admin:</span> Complete control over all aspects of the system</li>
                                        <li class="mb-2"><span class="fw-medium">Admin:</span> Manage users, content, and settings</li>
                                        <li class="mb-2"><span class="fw-medium">Editor:</span> Edit and publish content created by others</li>
                                        <li class="mb-2"><span class="fw-medium">Author:</span> Create and edit their own content</li>
                                        <li class="mb-2"><span class="fw-medium">Subscriber:</span> Access to premium content</li>
                                        <li class="mb-2"><span class="fw-medium">Student:</span> Access to courses and learning materials</li>
                                    </ul>
                                </div>

                                <div>
                                    <h6 class="text-color-2 fw-medium">Important Notes</h6>
                                    <ul class="list-unstyled mt-3">
                                        <li class="mb-2"><i class="fa-solid fa-circle-info text-primary me-2"></i> All fields marked with <span class="text-danger">*</span> are required</li>
                                        <li class="mb-2"><i class="fa-solid fa-circle-info text-primary me-2"></i> Password must be at least 8 characters</li>
                                        <li class="mb-2"><i class="fa-solid fa-circle-info text-primary me-2"></i> Choose appropriate roles based on user responsibilities</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php include 'include/footer.php'; ?>
