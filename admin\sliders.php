<?php
// Start session
session_start();

// Check if the user is authenticated
if (!isset($_SESSION['auth_user']) || empty($_SESSION['auth_user']['id'])) {
    // User is not logged in, redirect to login page
    header('Location: login.php');
    exit; // Make sure to exit to prevent further script execution
}

// Include database connection
require_once 'database/conn.php';

// Create a simple database wrapper class for this file
class SimpleDB {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    public function fetchRow($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }

    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($data);

        return $this->pdo->lastInsertId();
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }

    public function update($table, $data, $where, $params = []) {
        $set = implode(', ', array_map(function($key) {
            return "{$key} = :{$key}";
        }, array_keys($data)));

        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);

        // Merge data and where params
        $allParams = array_merge($data, $params);
        return $stmt->execute($allParams);
    }
}

// Create database instance
$db = new SimpleDB($pdo);

// Initialize variables
$success_message = '';
$error_message = '';
$form_data = [];



// Handle form submission for creating new slider
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['delete_slider'])) {
    try {
        // Validate and sanitize input data
        $subtitle = trim(htmlspecialchars($_POST['subtitle'] ?? '', ENT_QUOTES, 'UTF-8'));
        $title = trim(htmlspecialchars($_POST['title'] ?? '', ENT_QUOTES, 'UTF-8'));
        $description = trim(htmlspecialchars($_POST['description'] ?? '', ENT_QUOTES, 'UTF-8'));

        // Store form data for repopulation on error
        $form_data = [
            'subtitle' => $subtitle,
            'title' => $title,
            'description' => $description
        ];

        // Validation
        $errors = [];

        if (empty($subtitle)) $errors[] = "Subtitle is required";
        if (empty($title)) $errors[] = "Title is required";
        if (empty($description)) $errors[] = "Description is required";

        // Handle file upload - Store only file path in database
        $image_path = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/sliders/';

            // Create upload directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            // Validate file type
            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = "Only JPG, JPEG, PNG, GIF, and WebP files are allowed";
            }
            // Validate file size (5MB limit)
            elseif ($_FILES['image']['size'] > 5 * 1024 * 1024) {
                $errors[] = "File size must be less than 5MB";
            }
            // Validate actual image file (security check)
            elseif (!getimagesize($_FILES['image']['tmp_name'])) {
                $errors[] = "Invalid image file";
            }
            else {
                // Generate unique filename to prevent conflicts
                $unique_filename = uniqid('slider_', true) . '.' . $file_extension;
                $upload_path = $upload_dir . $unique_filename;

                // Move uploaded file to destination
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    // Store only the relative path in database
                    $image_path = $upload_path;
                } else {
                    $errors[] = "Failed to upload slider image";
                }
            }
        } else {
            $errors[] = "Slider image is required";
        }

        // If no errors, insert slider
        if (empty($errors)) {
            $slider_data = [
                'subtitle' => $subtitle,
                'title' => $title,
                'description' => $description,
                'image' => $image_path
            ];

            $slider_id = $db->insert('sliders', $slider_data);

            if ($slider_id) {
                $_SESSION['success_message'] = "Slider created successfully! Slider ID: " . $slider_id;
                // Redirect to prevent resubmission
                header('Location: sliders.php');
                exit;
            } else {
                $error_message = "Failed to create slider. Please try again.";
            }
        } else {
            $error_message = implode('<br>', $errors);
        }

    } catch (Exception $e) {
        error_log("Slider creation error: " . $e->getMessage());
        $error_message = "An error occurred while creating the slider. Please try again.";
    }
}

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    try {
        $slider_id = (int)$_GET['id'];

        // Get slider image path before deleting
        $slider = $db->fetchRow("SELECT image FROM sliders WHERE id = ?", [$slider_id]);

        if ($slider && $db->delete('sliders', 'id = ?', [$slider_id])) {
            // Delete the image file if it exists
            if ($slider['image'] && file_exists($slider['image'])) {
                unlink($slider['image']);
            }
            $_SESSION['success_message'] = "Slider deleted successfully!";
        } else {
            $_SESSION['error_message'] = "Failed to delete slider.";
        }
    } catch (Exception $e) {
        error_log("Slider deletion error: " . $e->getMessage());
        $_SESSION['error_message'] = "An error occurred while deleting the slider.";
    }

    // Redirect to prevent resubmission
    header('Location: sliders.php');
    exit;
}

// Handle delete form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_slider'])) {
    try {
        $slider_id = (int)$_POST['slider_id'];

        // Get slider image path before deleting
        $slider = $db->fetchRow("SELECT image FROM sliders WHERE id = ?", [$slider_id]);

        if ($slider && $db->delete('sliders', 'id = ?', [$slider_id])) {
            // Delete the image file if it exists
            if ($slider['image'] && file_exists($slider['image'])) {
                unlink($slider['image']);
            }
            $_SESSION['success_message'] = "Slider deleted successfully!";
        } else {
            $_SESSION['error_message'] = "Failed to delete slider.";
        }
    } catch (Exception $e) {
        error_log("Slider deletion error: " . $e->getMessage());
        $_SESSION['error_message'] = "An error occurred while deleting the slider.";
    }

    // Redirect to prevent resubmission
    header('Location: sliders.php');
    exit;
}

// Fetch all sliders for display
try {
    $sliders = $db->fetchAll("SELECT * FROM sliders ORDER BY created_at DESC");
} catch (Exception $e) {
    $sliders = [];
    error_log("Error fetching sliders: " . $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Slider Management | Admin Dashboard</title>
    <!-- Stylesheets -->
    <link rel="shortcut icon" href="./assets/images/favicon.ico" type="image/x-icon">
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/brands.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
    <link href="./assets/plugin/datatable/datatables.min.css" rel="stylesheet">
    <link href="./assets/css/style.css" rel="stylesheet">
    <style>
        .slider-img {
            width: 80px;
            height: 50px;
            border-radius: 4px;
            object-fit: cover;
        }
        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
            color: #fff;
        }
        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
            color: #fff;
        }
        .btn-primary {
            background-color: #2a1c19;
            border-color: #2a1c19;
        }
        .btn-primary:hover {
            background-color: #1a100d;
            border-color: #1a100d;
        }
        .dataTables_wrapper .dataTables_filter input {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin-left: 5px;
        }
        .dataTables_wrapper .dataTables_length select {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin: 0 5px;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #2a1c19;
            color: #fff !important;
            border: 1px solid #2a1c19;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #1a100d;
            color: #fff !important;
            border: 1px solid #1a100d;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .description-cell {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <!-- Preloader -->
    <div id="preloader">
        <div class="spinner"></div>
    </div>
    <!-- Main Wrapper -->
    <div id="main-wrapper" class="d-flex">
       <!--sidebar here -->
           <?php include 'include/sidebar.php'; ?>
       <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Header -->
            <div class="header d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="collapse-sidebar me-3 d-none d-lg-block text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="menu-toggle me-3 d-block d-lg-none text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="d-none d-md-block d-lg-block">
                        <div class="input-group flex-nowrap">
                            <span class="input-group-text bg-white " id="addon-wrapping"><i class="fa-solid search-icon fa-magnifying-glass text-color-1"></i></span>
                            <input type="text" class="form-control search-input border-l-none ps-0" placeholder="Search anything" aria-label="Username" aria-describedby="addon-wrapping">
                        </div>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <ul class="nav d-flex align-items-center">
                         <!-- User Profile -->
                        <li class="nav-item dropdown user-profile">
                            <div class="d-flex align-items-center dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="user-avatar me-0 me-lg-3">
                                    <?php
                                    // Get profile image path and create proper URL
                                    $profile_image_path = isset($_SESSION['auth_user']['profile_image']) ? $_SESSION['auth_user']['profile_image'] : '';
                                    $profile_image_url = '';

                                    if (!empty($profile_image_path)) {
                                        // Extract filename from path regardless of directory structure
                                        $filename = basename($profile_image_path);

                                        // Always use the uploads/profiles directory with the extracted filename
                                        $corrected_path = './uploads/profiles/' . $filename;

                                        // Check if file exists in corrected path
                                        if (file_exists($corrected_path)) {
                                            $profile_image_url = $corrected_path;
                                        }
                                        // If corrected path doesn't work, try the original path
                                        elseif (file_exists($profile_image_path)) {
                                            $profile_image_url = $profile_image_path;
                                        }
                                    }

                                    // Display image if we have a valid URL
                                    if (!empty($profile_image_url)): ?>
                                        <img src="<?php echo htmlspecialchars($profile_image_url); ?>" alt="User" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <!-- Fallback to letter avatar with proper styling -->
                                        <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                                             style="width: 40px; height: 40px; background-color: #6c757d; font-size: 16px;">
                                            <?php
                                            if (isset($_SESSION['auth_user']['name']) && !empty($_SESSION['auth_user']['name'])) {
                                                echo strtoupper(substr(htmlspecialchars($_SESSION['auth_user']['name']), 0, 1));
                                            } else {
                                                echo 'A'; // Default initial
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </span>
                                <div>
                                    <a href="#" class="d-none d-lg-block">
                                        <span class="d-block auth-role">
                                            <?php
                                                if (isset($_SESSION['auth_user']['role'])) {
                                                    echo htmlspecialchars(ucfirst(str_replace('_', ' ', $_SESSION['auth_user']['role'])));
                                                } else {
                                                    echo 'Guest'; // Default role
                                                }
                                            ?>
                                        </span>
                                        <span class="auth-name">
                                            <?php echo isset($_SESSION['auth_user']['name']) ? htmlspecialchars($_SESSION['auth_user']['name']) : 'Guest User'; // Default name ?>
                                        </span>
                                        <span class="ms-2 text-color-1 text-size-sm"><i class="fa-solid fa-angle-down"></i></span>
                                    </a>
                                    <ul class="dropdown-menu mt-3">
                                        <li><a class="dropdown-item" href="#">Profile</a></li>
                                        <li><a class="dropdown-item" href="#">Settings</a></li>
                                        <li><hr class="dropdown-divider"></li>

                                    </ul>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex align-items-lg-center flex-column flex-md-row flex-lg-row mt-3">
                            <div class="flex-grow-1">
                                <h3 class="mb-2 text-color-2">Slider Management</h3>
                                <p class="text-muted">Create and manage homepage sliders</p>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                        <i class="fa-solid fa-check-circle me-2"></i>
                        <?php
                        echo $_SESSION['success_message'];
                        unset($_SESSION['success_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                        <i class="fa-solid fa-exclamation-triangle me-2"></i>
                        <?php
                        echo $_SESSION['error_message'];
                        unset($_SESSION['error_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

              <!-- Slider Creation Form -->
              <div class="row mt-4">
                  <div class="col-12">
                      <div class="card shadow-sm border-0">
                          <div class="card-header bg-white py-3">
                              <h5 class="card-title mb-0 text-color-2">
                                  <i class="fas fa-plus-circle me-2"></i>Add New Slider
                              </h5>
                          </div>
                          <div class="card-body p-4">
                              <?php if (!empty($success_message)): ?>
                                  <div class="alert alert-success alert-dismissible fade show" role="alert">
                                      <i class="fa-solid fa-check-circle me-2"></i>
                                      <?php echo $success_message; ?>
                                      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                  </div>
                              <?php endif; ?>

                              <?php if (!empty($error_message)): ?>
                                  <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                      <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                      <?php echo $error_message; ?>
                                      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                  </div>
                              <?php endif; ?>

                              <form action="" method="post" enctype="multipart/form-data" class="row g-4">
                                  <!-- Subtitle Field -->
                                  <div class="col-md-6">
                                      <label for="subtitle" class="form-label text-color-2 fw-medium">Subtitle <span class="text-danger">*</span></label>
                                      <input type="text" class="form-control" id="subtitle" name="subtitle" placeholder="Enter slider subtitle" value="<?php echo htmlspecialchars($form_data['subtitle'] ?? '', ENT_QUOTES); ?>" required>
                                  </div>

                                  <!-- Title Field -->
                                  <div class="col-md-6">
                                      <label for="title" class="form-label text-color-2 fw-medium">Title <span class="text-danger">*</span></label>
                                      <input type="text" class="form-control" id="title" name="title" placeholder="Enter slider title" value="<?php echo htmlspecialchars($form_data['title'] ?? '', ENT_QUOTES); ?>" required>
                                  </div>

                                  <!-- Description Field -->
                                  <div class="col-12">
                                      <label for="description" class="form-label text-color-2 fw-medium">Description <span class="text-danger">*</span></label>
                                      <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter slider description" required><?php echo htmlspecialchars($form_data['description'] ?? '', ENT_QUOTES); ?></textarea>
                                  </div>

                                  <!-- Image Field -->
                                  <div class="col-12">
                                      <label for="image" class="form-label text-color-2 fw-medium">Slider Image <span class="text-danger">*</span></label>
                                      <input type="file" class="form-control" id="image" name="image" accept="image/*" required>
                                      <small class="text-muted">
                                          <i class="fa-solid fa-info-circle me-1"></i>
                                          Upload a slider image.
                                          <strong>Supported formats:</strong> JPG, JPEG, PNG, GIF, WebP.
                                          <strong>Max size:</strong> 5MB.
                                          <strong>Recommended size:</strong> 1920x800px for best results.
                                      </small>
                                  </div>

                                  <!-- Submit Button -->
                                  <div class="col-12 mt-4">
                                      <button type="submit" class="btn bg-primary text-white px-4 py-2">
                                          <i class="fa-solid fa-plus me-2"></i> Create Slider
                                      </button>
                                      <button type="reset" class="btn btn-light border ms-2 px-4 py-2">
                                          <i class="fa-solid fa-rotate me-2"></i> Reset
                                      </button>
                                  </div>
                              </form>
                          </div>
                      </div>
                  </div>
              </div>

                <!-- Sliders Table -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle" id="slidersTable">
                                        <thead class="bg-light">
                                            <tr>
                                                <th scope="col" class="py-3">ID</th>
                                                <th scope="col" class="py-3">Image</th>
                                                <th scope="col" class="py-3">Subtitle</th>
                                                <th scope="col" class="py-3">Title</th>
                                                <th scope="col" class="py-3">Description</th>
                                                <th scope="col" class="py-3">Created Date</th>
                                                <th scope="col" class="py-3 text-center">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($sliders)): ?>
                                                <?php foreach ($sliders as $slider): ?>
                                                    <tr data-slider-id="<?php echo $slider['id']; ?>">
                                                        <td>
                                                            <span class="fw-medium text-color-2">#<?php echo $slider['id']; ?></span>
                                                        </td>
                                                        <td>
                                                            <?php if ($slider['image'] && file_exists($slider['image'])): ?>
                                                                <img src="<?php echo htmlspecialchars($slider['image']); ?>"
                                                                     alt="Slider Image"
                                                                     class="slider-img"
                                                                     data-bs-toggle="tooltip"
                                                                     title="<?php echo htmlspecialchars($slider['title']); ?>">
                                                            <?php else: ?>
                                                                <div class="bg-light rounded d-flex align-items-center justify-content-center slider-img">
                                                                    <i class="fas fa-image text-muted"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <span class="text-color-2"><?php echo htmlspecialchars($slider['subtitle']); ?></span>
                                                        </td>
                                                        <td>
                                                            <span class="fw-medium text-color-2"><?php echo htmlspecialchars($slider['title']); ?></span>
                                                        </td>
                                                        <td>
                                                            <div class="description-cell" title="<?php echo htmlspecialchars($slider['description']); ?>">
                                                                <?php echo htmlspecialchars($slider['description']); ?>
                                                            </div>
                                                        </td>
                                                        <td><?php echo date('M d, Y', strtotime($slider['created_at'])); ?></td>
                                                        <td class="text-center">
                                                            <a href="#" data-bs-toggle="modal" data-bs-target="#viewSliderModal"
                                                               data-id="<?php echo $slider['id']; ?>"
                                                               data-subtitle="<?php echo htmlspecialchars($slider['subtitle']); ?>"
                                                               data-title="<?php echo htmlspecialchars($slider['title']); ?>"
                                                               data-description="<?php echo htmlspecialchars($slider['description']); ?>"
                                                               data-image="<?php echo htmlspecialchars($slider['image']); ?>"
                                                               data-created="<?php echo htmlspecialchars($slider['created_at']); ?>"
                                                               data-updated="<?php echo htmlspecialchars($slider['updated_at']); ?>"
                                                               class="btn btn-sm btn-info me-2 view-slider">
                                                                <i class="fa-regular fa-eye"></i>
                                                            </a>
                                                            <a href="#" data-bs-toggle="modal" data-bs-target="#editSliderModal"
                                                               data-id="<?php echo $slider['id']; ?>"
                                                               data-subtitle="<?php echo htmlspecialchars($slider['subtitle']); ?>"
                                                               data-title="<?php echo htmlspecialchars($slider['title']); ?>"
                                                               data-description="<?php echo htmlspecialchars($slider['description']); ?>"
                                                               data-image="<?php echo htmlspecialchars($slider['image']); ?>"
                                                               class="btn btn-sm btn-primary me-2 edit-slider">
                                                                <i class="fa-regular fa-pen-to-square"></i>
                                                            </a>
                                                            <a href="#" data-bs-toggle="modal" data-bs-target="#deleteSliderModal"
                                                               data-slider-id="<?php echo $slider['id']; ?>"
                                                               data-slider-title="<?php echo htmlspecialchars($slider['title']); ?>"
                                                               class="btn btn-sm btn-danger delete-slider">
                                                                <i class="fa-solid fa-trash-can"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="7" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="fa-solid fa-images fa-2x mb-3"></i>
                                                            <p>No sliders found in the system.</p>
                                                            <p class="small">Create your first slider using the form above.</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- View Slider Modal -->
                <div class="modal fade" id="viewSliderModal" tabindex="-1" aria-labelledby="viewSliderModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="viewSliderModalLabel">Slider Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <img id="viewSliderImage" src="" class="img-fluid rounded" alt="Slider Image">
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Subtitle:</label>
                                            <p id="viewSliderSubtitle"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Title:</label>
                                            <p id="viewSliderTitle"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Description:</label>
                                            <p id="viewSliderDescription"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Created:</label>
                                            <p id="viewSliderCreated"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Last Updated:</label>
                                            <p id="viewSliderUpdated"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Edit Slider Modal -->
                <div class="modal fade" id="editSliderModal" tabindex="-1" aria-labelledby="editSliderModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="editSliderModalLabel">Edit Slider</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editSliderForm" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="slider_id" id="edit_slider_id">

                                    <div class="row g-3">
                                        <!-- Subtitle Field -->
                                        <div class="col-md-6">
                                            <label for="edit_subtitle" class="form-label">Subtitle</label>
                                            <input type="text" class="form-control" id="edit_subtitle" name="subtitle" required>
                                        </div>

                                        <!-- Title Field -->
                                        <div class="col-md-6">
                                            <label for="edit_title" class="form-label">Title</label>
                                            <input type="text" class="form-control" id="edit_title" name="title" required>
                                        </div>

                                        <!-- Description Field -->
                                        <div class="col-12">
                                            <label for="edit_description" class="form-label">Description</label>
                                            <textarea class="form-control" id="edit_description" name="description" rows="4" required></textarea>
                                        </div>

                                        <!-- Current Image Display -->
                                        <div class="col-12">
                                            <label class="form-label">Current Image</label>
                                            <div class="mb-3">
                                                <img id="current_slider_image" src="" class="img-thumbnail" style="max-width: 200px; max-height: 120px;" alt="Current Image">
                                            </div>
                                        </div>

                                        <!-- New Image Field -->
                                        <div class="col-12">
                                            <label for="edit_slider_image" class="form-label">New Image (Optional)</label>
                                            <input type="file" class="form-control" id="edit_slider_image" name="image" accept="image/*">
                                            <small class="text-muted">
                                                <i class="fa-solid fa-info-circle me-1"></i>
                                                Leave empty to keep current image. Supported formats: JPG, JPEG, PNG, GIF, WebP. Max size: 5MB.
                                            </small>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" form="editSliderForm" class="btn btn-primary">Save Changes</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Slider Modal -->
                <div class="modal fade" id="deleteSliderModal" tabindex="-1" aria-labelledby="deleteSliderModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="deleteSliderModalLabel">Confirm Delete</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>Are you sure you want to delete <span id="deleteSliderTitle" class="fw-bold"></span>? This action cannot be undone.</p>
                                <div class="alert alert-danger">
                                    <div class="d-flex align-items-center">
                                        <i class="fa-solid fa-triangle-exclamation me-2"></i>
                                        <div>
                                            <strong>Warning:</strong> The slider and its associated image will be permanently deleted.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="slider_id" id="delete_slider_id">
                                    <input type="hidden" name="delete_slider" value="1">
                                    <button type="submit" class="btn btn-danger">Delete Slider</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
             <!-- Footer -->
             <div class="footer text-center bg-white shadow-sm py-3 mt-5">
                <p class="m-0">Copyright © 2025. All Rights Reserved. <a href="https://finalskills.com" class="text-primary" target="_blank" >Developed by final skills</a></p>
            </div>
        </div>
    </div>
     <!-- Scripts -->
    <script  src="./assets/js/jquery-3.6.0.min.js"></script>
    <script  src="./assets/js/bootstrap.bundle.min.js"></script>
    <script  src="./assets/plugin/chart/chart.js"></script>
    <script src="./assets/plugin/datatable/datatables.min.js"></script>
    <script  src="./assets/js/chart.js"></script>
    <script  src="./assets/js/main.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize DataTable with pagination
            const sliderTable = $('#slidersTable').DataTable({
                responsive: true,
                order: [[0, 'desc']], // Sort by ID column by default (newest first)
                columnDefs: [
                    { orderable: false, targets: [1, 6] } // Disable sorting for image and actions columns
                ],
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Search sliders...",
                    lengthMenu: "Show _MENU_ sliders per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ sliders",
                    infoEmpty: "No sliders found",
                    infoFiltered: "(filtered from _MAX_ total sliders)"
                },
                // Enable pagination
                paging: true,
                pagingType: "full_numbers",
                pageLength: 10,
                lengthMenu: [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]]
            });

            // Handle view slider modal
            $(document).on('click', '.view-slider', function(e) {
                e.preventDefault();

                const $this = $(this);
                const sliderData = {
                    id: $this.data('id'),
                    subtitle: $this.data('subtitle'),
                    title: $this.data('title'),
                    description: $this.data('description'),
                    image: $this.data('image'),
                    created_at: $this.data('created'),
                    updated_at: $this.data('updated')
                };

                // Set the slider image
                $('#viewSliderImage').attr('src', sliderData.image || './assets/images/placeholder.png');

                // Set slider details
                $('#viewSliderSubtitle').text(sliderData.subtitle || 'Not provided');
                $('#viewSliderTitle').text(sliderData.title || 'Not provided');
                $('#viewSliderDescription').text(sliderData.description || 'No description provided');

                // Format the dates
                try {
                    const createdDate = sliderData.created_at ? new Date(sliderData.created_at).toLocaleString() : 'Unknown';
                    const updatedDate = sliderData.updated_at ? new Date(sliderData.updated_at).toLocaleString() : 'Unknown';
                    $('#viewSliderCreated').text(createdDate);
                    $('#viewSliderUpdated').text(updatedDate);
                } catch (error) {
                    $('#viewSliderCreated').text(sliderData.created_at || 'Unknown');
                    $('#viewSliderUpdated').text(sliderData.updated_at || 'Unknown');
                }

                // Show the modal
                $('#viewSliderModal').modal('show');
            });

            // Handle edit slider modal
            $(document).on('click', '.edit-slider', function(e) {
                e.preventDefault();

                const $this = $(this);
                const sliderData = {
                    id: $this.data('id'),
                    subtitle: $this.data('subtitle'),
                    title: $this.data('title'),
                    description: $this.data('description'),
                    image: $this.data('image')
                };

                // Populate the edit form
                $('#edit_slider_id').val(sliderData.id);
                $('#edit_subtitle').val(sliderData.subtitle);
                $('#edit_title').val(sliderData.title);
                $('#edit_description').val(sliderData.description);

                // Set current image
                $('#current_slider_image').attr('src', sliderData.image || './assets/images/placeholder.png');

                // Show the modal
                $('#editSliderModal').modal('show');
            });

            // Handle delete slider modal
            $(document).on('click', '.delete-slider', function(e) {
                e.preventDefault();
                const sliderId = $(this).data('slider-id');
                const sliderTitle = $(this).data('slider-title');
                $('#delete_slider_id').val(sliderId);
                $('#deleteSliderTitle').text(sliderTitle);
                $('#deleteSliderModal').modal('show');
            });

            // Handle edit slider form submission
            $('#editSliderForm').on('submit', function(e) {
                e.preventDefault();

                // Show loading indicator
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fa-solid fa-spinner fa-spin me-2"></i> Saving...');
                submitBtn.prop('disabled', true);

                const formData = new FormData(this);

                $.ajax({
                    url: 'update-slider.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        let data;

                        try {
                            data = (typeof response === 'object') ? response : JSON.parse(response);
                        } catch (e) {
                            console.error('Error parsing JSON response:', e);
                            data = { success: false, message: 'Invalid server response' };
                        }

                        if (data.success) {
                            // Show success message before reload
                            const alertHtml = `
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fa-solid fa-check-circle me-2"></i>
                                    ${data.message}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            `;

                            // Close modal
                            $('#editSliderModal').modal('hide');

                            // Show message and reload after a short delay
                            $('<div class="mt-3"></div>').html(alertHtml).insertAfter('.d-flex.align-items-lg-center');
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            // Show error message
                            const errorHtml = `
                                <div class="alert alert-danger mt-3 mb-0">
                                    <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                    ${data.message || 'Error updating slider'}
                                </div>
                            `;

                            // Replace any existing error message
                            $('#editSliderForm .alert').remove();
                            $(errorHtml).prependTo('#editSliderForm');

                            // Reset button
                            submitBtn.html(originalText);
                            submitBtn.prop('disabled', false);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);

                        // Show error message
                        const errorHtml = `
                            <div class="alert alert-danger mt-3 mb-0">
                                <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                Error updating slider: ${error || 'Unknown error'}
                            </div>
                        `;

                        // Replace any existing error message
                        $('#editSliderForm .alert').remove();
                        $(errorHtml).prependTo('#editSliderForm');

                        // Reset button
                        submitBtn.html(originalText);
                        submitBtn.prop('disabled', false);
                    }
                });
            });
        });
    </script>
</body>
</html>
