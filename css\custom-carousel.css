/* Custom Carousel Styles for Product Grid */
.product-carousel.product-grid {
    display: block !important;
    padding: 0 15px;
}

.product-carousel .product-item {
    width: 100% !important;
    padding: 15px;
    float: none;
}

.product-carousel .owl-stage {
    display: flex;
}

.product-carousel .owl-item {
    display: flex;
    flex: 1 0 auto;
    min-width: 280px; /* Ensure minimum width for items */
}

.product-carousel .owl-item .product-item {
    width: 100%;
    max-width: 100%;
}

.product-carousel .product-single {
    width: 100%;
    display: flex;
    flex-direction: column;
}

/* Ensure proper spacing and alignment for navigation controls */
.product-carousel .owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    margin-top: -25px;
    display: flex;
    justify-content: space-between;
    z-index: 1;
}

.product-carousel .owl-nav button {
    width: 40px;
    height: 40px;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1dbf71;
    background: #FFFFFF;
    border: 1px solid #1dbf71 !important;
    border-radius: 50px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    font-size: 22px;
    transition: .5s;
    position: absolute;
}

.product-carousel .owl-nav button.owl-prev {
    left: -10px;
}

.product-carousel .owl-nav button.owl-next {
    right: -10px;
}

.product-carousel .owl-nav button:hover {
    background: #1dbf71;
    color: #FFFFFF;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .product-carousel .owl-nav {
        margin-top: -20px;
    }
    
    .product-carousel .owl-nav button {
        width: 30px;
        height: 30px;
        font-size: 18px;
    }
}

@media (max-width: 576px) {
    .product-carousel .owl-nav button {
        display: none;
    }
}
