<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Form Element</title>
    <!-- Stylesheets -->
    <link rel="shortcut icon" href="./assets/images/favicon.ico" type="image/x-icon">
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/brands.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
    <link href="./assets/plugin/quill/quill.snow.css" rel="stylesheet">
    <link href="./assets/plugin/tagify/css/tagify.css" rel="stylesheet">
    <link href="./assets/plugin/select2/css/select2.min.css" rel="stylesheet">
    <link href="./assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Preloader -->
    <div id="preloader">
        <div class="spinner"></div>
    </div>
    <!-- Main Wrapper -->
    <div id="main-wrapper" class="d-flex">
        <div class="sidebar">
                <!-- Sidebar -->
                <div class="sidebar-header">
                  <div class="lg-logo"><a href="index.html"><img src="./assets/images/logo.png" alt="logo large"></a></div>
                  <div class="sm-logo"><a href="index.html"><img src="./assets/images/small-logo.png" alt="logo small"></a></div>
               </div>
               <div class="sidebar-body  custom-scrollbar">
                    <ul class="sidebar-menu">
                      <li><a href="index.html" class="sidebar-link"><i class="fa-solid fa-house"></i><p>Dashboard</p></a></li>
                      <li><a href="course.html" class="sidebar-link"><i class="fa-brands fa-discourse"></i><p>Courses</p></a></li>
                      <li><a href="students.html" class="sidebar-link"><i class="fa-solid fa-user"></i><p>Students</p></a></li>
                      <li><a href="teacher.html" class="sidebar-link"><i class="fa-solid fa-chalkboard-user"></i><p>Teachers</p></a></li>
                      <li><a href="library.html" class="sidebar-link"><i class="fa-solid fa-book"></i><p>Library</p></a></li>
                      <li><a href="department.html" class=" sidebar-link"><i class="fa-solid fa-building"></i><p>Department</p></a></li>
                      <li><a href="staff.html" class="sidebar-link"><i class="fa-solid fa-users"></i><p>Staff</p></a></li>
                      <li><a href="fees.html" class="sidebar-link"><i class="fa-solid fa-dollar-sign"></i><p>Fees</p></a></li>
                      <li><a href="#" class=" sidebar-link submenu-parent"><i class="fa-solid fa-list"></i><p>Pages <i class="fa-solid fa-chevron-right right-icon"></i></p></a>
                        <ul class="sidebar-submenu">
                            <li><a href="login.html" class="submenu-link"><i class="fa-solid fa-circle me-4 font-size-12"></i><p class="m-0">Login</p></a></li>
                            <li><a href="signup.html" class="submenu-link"><i class="fa-solid fa-circle me-4 font-size-12"></i><p class="m-0">Register</p></a></li>
                            <li><a href="forgot-password.html" class="submenu-link"><i class="fa-solid fa-circle me-4 font-size-12"></i><p class="m-0">Forgot password</p></a></li>
                            <li><a href="404.html" class="submenu-link"><i class="fa-solid fa-circle me-4 font-size-12"></i><p class="m-0">404 page</p></a></li>
                            <li><a href="500.html" class="submenu-link"><i class="fa-solid fa-circle me-4 font-size-12"></i><p class="m-0">500 page</p></a></li>
                        </ul>
                      </li>
                      <li><a href="#" class="sidebar-link submenu-parent"><i class="fa-solid fa-list"></i><p>Table <i class="fa-solid fa-chevron-right right-icon"></i></p></a>
                        <ul class="sidebar-submenu">
                            <li><a href="table-bootstrap.html" class="submenu-link"><i class="fa-solid fa-circle me-4 font-size-12"></i><p class="m-0">Bootstrap</p></a></li>
                            <li><a href="data-table.html" class="submenu-link"><i class="fa-solid fa-circle me-4 font-size-12"></i><p class="m-0">DataTable</p></a></li>
                        </ul>
                    </li>
                    <li><a href="#" class="sidebar-link active submenu-parent"><i class="fa-solid fa-list"></i><p>Components <i class="fa-solid fa-chevron-right right-icon"></i></p></a>
                      <ul class="sidebar-submenu open">
                          <li><a href="form.html" class="submenu-link active"><i class="fa-solid fa-circle me-4 font-size-12"></i><p class="m-0">Form Element</p></a></li>
                      </ul>
                    </li>
                    </ul>
               </div>
        </div>
       <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Header -->
            <div class="header d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="collapse-sidebar me-3 d-none d-lg-block text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="menu-toggle me-3 d-block d-lg-none text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="d-none d-md-block d-lg-block">
                        <div class="input-group flex-nowrap">
                            <span class="input-group-text bg-white " id="addon-wrapping"><i class="fa-solid search-icon fa-magnifying-glass text-color-1"></i></span>
                            <input type="text" class="form-control search-input border-l-none ps-0" placeholder="Search anything" aria-label="Username" aria-describedby="addon-wrapping">
                        </div>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <ul class="nav d-flex align-items-center">
                        <!-- Messages Dropdown -->
                        <li class="nav-item me-2-5">
                            <a href="#" class="text-color-1 position-relative"  role="button" 
                            data-bs-toggle="dropdown" 
                            data-bs-offset="0,0" 
                            aria-expanded="false">
                            <i class="fa-regular fa-message font-size-24"></i>
                        </a>
                            <div class="dropdown-menu dropdown-menu-end mt-4">
                                <div id="chatmessage" class="h-380 scroll-y p-3 custom-scrollbar">
                                    <!-- Chat Timeline -->
                                    <ul class="timeline">
                                        <!-- Item 1 -->
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2">
                                                    <img alt="image" width="50" src="./assets/images/avatar-1.jpg">
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">We talked about a project...</h6>
                                                    <small class="d-block"><i class="fa-solid fa-clock"></i> 30 min ago</small>
                                                </div>
                                            </div>
                                        </li>
                                        <!-- Item 2 -->
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2">
                                                    <img alt="image" width="50" src="./assets/images/avatar-2.jpg">
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">You sent an email to the client...</h6>
                                                    <small class="d-block"><i class="fa-solid fa-clock"></i> 1 hour ago</small>
                                                </div>
                                            </div>
                                        </li>
                                        <!-- Item 3 -->
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2">
                                                    <img alt="image" width="50" src="./assets/images/avatar-3.jpg">
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">Meeting with the design team...</h6>
                                                    <small class="d-block"><i class="fa-solid fa-clock"></i> 2 hours ago</small>
                                                </div>
                                            </div>
                                        </li>
                                        <!-- Item 4 -->
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2">
                                                    <img alt="image" width="50" src="./assets/images/avatar-4.jpg">
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">Reviewed the project documents...</h6>
                                                    <small class="d-block"><i class="fa-solid fa-clock"></i> Yesterday</small>
                                                </div>
                                            </div>
                                        </li>
                                        <!-- Item 5 -->
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2">
                                                    <img alt="image" width="50" src="./assets/images/avatar-5.jpg">
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">Finalized the project timeline...</h6>
                                                    <small class="d-block"><i class="fa-solid fa-clock"></i> 2 days ago</small>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                                <a class="all-notification" href="#">See all message <i class="fas fa-arrow-right"></i></a>
                            </div>
                        </li>
                        <!-- Notifications Dropdown -->
                        <li class="nav-item me-2-5">
                            <a href="#" class="text-color-1 notification" 
                                role="button" 
                                data-bs-toggle="dropdown" 
                                data-bs-offset="0,0" 
                                aria-expanded="false">
                                <i class="fa-regular fa-bell font-size-24"></i>
                                <div class="marker"></div>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end mt-4">
                                <div id="Notification" class="h-380 scroll-y p-3 custom-scrollbar">
                                    <!-- Notifications Timeline -->
                                    <ul class="timeline">
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2">
                                                    <img alt="image" width="50" src="./assets/images/profile.png">
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">Dr Smith uploaded a new report</h6>
                                                    <small class="d-block">10 December 2023 - 08:15 AM</small>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2 media-info">
                                                    AP
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">New Appointment Scheduled</h6>
                                                    <small class="d-block">10 December 2023 - 09:45 AM</small>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2 media-success">
                                                    <i class="fa fa-check-circle"></i>
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">Patient checked in at reception</h6>
                                                    <small class="d-block">10 December 2023 - 10:20 AM</small>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2">
                                                    <img alt="image" width="50" src="./assets/images/profile.png">
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">Dr Alice shared a prescription</h6>
                                                    <small class="d-block">10 December 2023 - 11:00 AM</small>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2 media-danger">
                                                    EM
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">Emergency Alert: Critical Patient</h6>
                                                    <small class="d-block">10 December 2023 - 11:30 AM</small>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="timeline-panel">
                                                <div class="media me-2 media-primary">
                                                    <i class="fa fa-calendar-alt"></i>
                                                </div>
                                                <div class="media-body">
                                                    <h6 class="mb-1">Next Appointment Reminder</h6>
                                                    <small class="d-block">10 December 2023 - 12:00 PM</small>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                    
                                </div>
                                <a class="all-notification" href="#">See all notifications <i class="fas fa-arrow-right"></i></a>
                            </div>
                        </li>
                         <!-- User Profile -->
                        <li class="nav-item dropdown user-profile">
                            <div class="d-flex align-items-center dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="user-avatar me-0 me-lg-3">A</span>
                                <div>
                                    <a href="#" class="d-none d-lg-block">
                                        <span class="d-block auth-role">Adminitator</span>
                                        <span class="auth-name">Adin Lauren</span>
                                        <span class="ms-2 text-color-1 text-size-sm"><i class="fa-solid fa-angle-down"></i></span>
                                    </a>
                                    <ul class="dropdown-menu mt-3">
                                        <li><a class="dropdown-item" href="#">Profile</a></li>
                                        <li><a class="dropdown-item" href="#">Settings</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#">Logout</a></li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <!-- Main Content -->
            <div class="main-content">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex align-items-lg-center  flex-column flex-md-row flex-lg-row mt-3">
                            <div class="flex-grow-1">
                                <h3 class="mb-2 text-size-26 text-color-2">Form Element</h3>
                            </div>
                            <div class="mt-3 mt-lg-0">
                                
                            </div>
                        </div><!-- end card header -->
                    </div>
                    <!--end col-->
                </div>
                <div class="mt-4">
                  <div class="row">
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Form Input</h4>
                            </div>
                            <div class="card-body">
                              <form>
                                <div class="mb-3">
                                  <label for="exampleInputEmail1" class="form-label">Email address</label>
                                  <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
                                </div>
                                <div class="mb-3">
                                  <label for="exampleInputPassword1" class="form-label">Password</label>
                                  <input type="password" class="form-control" id="exampleInputPassword1">
                                </div>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Form select</h4>
                            </div>
                            <div class="card-body">
                              <form>
                                <select class="form-select form-select-lg mb-3" aria-label="Large select example">
                                  <option selected>Open this select menu</option>
                                  <option value="1">One</option>
                                  <option value="2">Two</option>
                                  <option value="3">Three</option>
                                </select>
                                <select class="form-select" aria-label="Default select example">
                                  <option selected>Open this select menu</option>
                                  <option value="1">One</option>
                                  <option value="2">Two</option>
                                  <option value="3">Three</option>
                                </select>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Form textarea</h4>
                            </div>
                            <div class="card-body">
                              <form>
                                <div class="mb-3">
                                  <label for="exampleFormControlTextarea1" class="form-label">Textarea</label>
                                  <textarea class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea>
                                </div>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Checkbox</h4>
                            </div>
                            <div class="card-body">
                              <form>
                                <div class="form-check mb-2">
                                  <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                  <label class="form-check-label" for="flexCheckDefault">
                                    Default checkbox
                                  </label>
                                </div>
                                <div class="form-check mb-2">
                                  <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked" checked>
                                  <label class="form-check-label" for="flexCheckChecked">
                                    Checked checkbox
                                  </label>
                                </div>
                                <div class="form-check mb-2 ps-0">
                                  <input type="checkbox" class="custom-checkbox" id="CustomCheck">
                                  <label class="form-check-label" for="CustomCheck">
                                    Custom checkbox
                                  </label>
                                </div>
                                <div class="form-check mb-2 ps-0">
                                  <input type="checkbox" class="custom-checkbox" id="CustomCheck1" checked>
                                  <label class="form-check-label" for="CustomCheck1">
                                    Custom checkbox
                                  </label>
                                </div>
                                <div class="form-check mb-2 form-switch">
                                  <input class="form-check-input" type="checkbox" id="flexSwitchCheck">
                                  <label class="form-check-label" for="flexSwitchCheck">Switch checkbox input</label>
                                </div>
                                <div class="form-check form-switch custom-switch">
                                  <input 
                                    class="form-check-input" 
                                    type="checkbox" 
                                    id="flexSwitchCheck1" checked>
                                  <label class="form-check-label" for="flexSwitchCheck1">Switch custom checkbox</label>
                                </div>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Radios</h4>
                            </div>
                            <div class="card-body">
                              <form>
                                <div class="form-check mb-2">
                                  <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1">
                                  <label class="form-check-label" for="flexRadioDefault1">
                                    Default radio
                                  </label>
                                </div>
                                <div class="form-check mb-2">
                                  <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2" checked>
                                  <label class="form-check-label" for="flexRadioDefault2">
                                    Default checked radio
                                  </label>
                                </div>
                                <div class="form-check mb-2 custom-radio">
                                  <input class="form-check-input" type="radio" name="flexRadioDefault1" id="flexRadioDefault3">
                                  <label class="form-check-label" for="flexRadioDefault3">
                                    Custom radio
                                  </label>
                                </div>
                                <div class="form-check mb-2 custom-radio">
                                  <input class="form-check-input" type="radio" name="flexRadioDefault1" id="flexRadioDefault4" checked>
                                  <label class="form-check-label" for="flexRadioDefault4">
                                    Custom checked radio
                                  </label>
                                </div>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">File input</h4>
                            </div>
                            <div class="card-body">
                              <form>
                                <div class="mb-3">
                                  <label for="formFile" class="form-label">File input example</label>
                                  <input class="form-control" type="file" id="formFile">
                                </div>
                                <div class="mb-3">
                                  <label for="formFileMultiple" class="form-label">Multiple files input example</label>
                                  <input class="form-control" type="file" id="formFileMultiple" multiple>
                                </div>
                                <div class="file-input-container">
                                  <span class="d-block mb-2">Custom file input</span>
                                  <input type="file" id="fileInput" class="file-input">
                                  <label for="fileInput" class="file-label">
                                    <span class="file-name">Choose file</span>
                                    <span class="file-button">Browse</span>
                                  </label>
                                </div>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Drag and drop file upload</h4>
                            </div>
                            <div class="card-body">
                              <form class="mt-2">
                                <div class="file-upload-container">
                                  <div id="dropzone" class="dropzone">
                                  <div class="icon">
                                      <svg width="90" height="90" viewBox="0 0 105 135" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <g clip-path="url(#clip0_12_2806)">
                                          <path d="M53.498 65.0693L15.9733 66.6661" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M53.8972 65.0693L103.398 69.4605" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M103.797 69.4607L93.8171 115.768" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M93.8171 115.768L52.6996 122.953" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M52.6996 122.554V73.4526" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M52.6996 73.8519L103.398 69.8599" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M52.6996 73.8518L15.9733 66.6663" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M15.1749 66.6663L17.5701 103.393" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M17.5701 103.393L52.6996 122.953" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M53.0988 65.0693V73.0533" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M82.5997 84.2736L74.5341 84.7725C72.6636 84.8882 71.2411 86.4982 71.3568 88.3687L71.4306 89.5624C71.5463 91.4328 73.1564 92.8553 75.0268 92.7396L83.0924 92.2408C84.9629 92.1251 86.3854 90.515 86.2697 88.6446L86.1958 87.4509C86.0802 85.5804 84.4701 84.1579 82.5997 84.2736Z" stroke="black" stroke-width="1.1976"></path>
                                          <path d="M1.20294 11.9761C8.24038 23.1729 27.476 44.381 48.1192 39.6388L77.0507 18.299" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M1.20294 11.9759L33.9372 1.19751" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M33.9373 1.19751C40.0777 6.78629 57.2968 17.9639 77.0507 17.9639" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M19.5661 17.5646C22.4048 16.8993 28.934 14.7703 32.3405 11.5767" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M27.9493 24.7503C29.7235 24.3511 33.8042 22.8342 35.9333 19.96" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M37.53 29.94C39.7922 29.5408 45.7535 27.7045 48.3084 21.9561" stroke="black" stroke-width="1.99599" stroke-linecap="round"></path>
                                          <path d="M52.4126 133.332L16.5721 103.991L52.5 123.951L94.8151 116.566L98.8071 117.165L52.4126 133.332Z" fill="#21325B" fill-opacity="0.3"></path>
                                          </g>
                                          <defs>
                                          <clipPath id="clip0_12_2806">
                                          <rect width="104.989" height="134.53" fill="white" transform="translate(0.00537109)"></rect>
                                          </clipPath>
                                          </defs>
                                      </svg>
                                  </div>
                                  <p>Drag and drop your file here</p>
                                  <p>or</p>
                                  <a href="javascript:void(0)" id="browseButton">Browse files</a>
                                  <input id="fileInput" type="file" hidden="">
                                  </div>
                                  <div id="preview" class="preview-grid"></div>
                              </div>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Form layout</h4>
                            </div>
                            <div class="card-body">
                              <form class="row g-3">
                                <div class="col-md-6">
                                  <label for="inputEmail4" class="form-label">Email</label>
                                  <input type="email" class="form-control" id="inputEmail4">
                                </div>
                                <div class="col-md-6">
                                  <label for="inputPassword4" class="form-label">Password</label>
                                  <input type="password" class="form-control" id="inputPassword4">
                                </div>
                                <div class="col-12">
                                  <label for="inputAddress" class="form-label">Address</label>
                                  <input type="text" class="form-control" id="inputAddress" placeholder="1234 Main St">
                                </div>
                                <div class="col-12">
                                  <div class="form-check mb-2 ps-0">
                                    <input type="checkbox" class="custom-checkbox" id="gridCheck">
                                    <label class="form-check-label" for="gridCheck">
                                      Check me out
                                    </label>
                                  </div>
                                </div>
                                <div class="col-12">
                                  <button type="submit" class="btn btn-primary">Sign in</button>
                                </div>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 min-h-150 pb-5">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Quill editor</h4>
                            </div>
                            <div class="card-body">
                              <form class="row g-3">
                                 <div class="col-12">
                                   <div class="editor"></div>
                                 </div>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Ckeditor</h4>
                            </div>
                            <div class="card-body">
                              <form class="mt-2">
                                <textarea id="ckeditor" class="form-control" ></textarea>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Tagify</h4>
                            </div>
                            <div class="card-body">
                              <form class="mt-2">
                                <input name='tag' class="input-tagify" value='tag1, tag2'>
                              </form>
                            </div> 
                          </div> 
                        </div>
                        <div class="col-lg-6 mb-4">
                          <div class="card shadow-sm border-0 h-100">
                            <div class="card-header bg-white pt-3">
                                <h4 class="h4">Select2</h4>
                            </div>
                            <div class="card-body">
                              <form class="mt-2">
                                <div class="col-md-12 mb-3">
                                  <label class="form-label">Single select</label>
                                  <select class="select2-single w-100" name="state">
                                    <option value="Alabama">Alabama</option>
                                    <option value="Alaska">Alaska</option>
                                    <option value="Arizona">Arizona</option>
                                    <option value="Arkansas">Arkansas</option>
                                    <option value="California">California</option>
                                    <option value="Colorado">Colorado</option>
                                    <option value="Connecticut">Connecticut</option>
                                  </select>
                                </div>
                                <div class="col-md-12 mb-3">
                                  <label class="form-label">Multiple select</label>
                                  <select class="select2-multiple w-100" name="country[]" multiple>
                                    <option value="US">United States</option>
                                    <option value="CA">Canada</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="AU">Australia</option>
                                    <option value="DE">Germany</option>
                                    <option value="FR">France</option>
                                    <option value="IN">India</option>
                                    <option value="JP">Japan</option>
                                    <option value="CN">China</option>
                                    <option value="BR">Brazil</option>
                                  </select>
                                </div>
                              </form>
                            </div> 
                          </div> 
                        </div>
                  </div>
                </div>
        </div>
         <!-- Footer -->
         <div class="footer text-center bg-white shadow-sm py-3 mt-5">
            <p class="m-0">Copyright © 2024. All Rights Reserved. <a href="https://www.templaterise.com/" class="text-primary" target="_blank" >Themes By TemplateRise</a></p>
        </div>
    </div>

    

     <!-- Scripts -->
    <script  src="./assets/js/jquery-3.6.0.min.js"></script>
    <script  src="./assets/js/bootstrap.bundle.min.js"></script>
    <script  src="./assets/plugin/chart/chart.js"></script>
    <script  src="./assets/plugin/quill/quill.js"></script>
    <script  src="./assets/js/chart.js"></script>
    <script src="./assets/plugin/ckeditor5/ckeditor.js"></script>
    <script src="./assets/plugin/tagify/js/tagify.js"></script>
    <script src="./assets/plugin/tagify/js/tagify.polyfills.min.js"></script>
    <script src="./assets/plugin/select2/js/select2.min.js"></script>
    <script  src="./assets/js/main.js"></script>
</body>
</html>
