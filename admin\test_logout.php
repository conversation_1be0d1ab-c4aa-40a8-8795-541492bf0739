<?php
/**
 * Test Logout Functionality
 * 
 * This file helps test the logout functionality by:
 * 1. Creating a test session
 * 2. Providing links to test logout
 * 3. Showing session status
 */

session_start();

// Create a test session if none exists
if (!isset($_SESSION['auth_user'])) {
    $_SESSION['auth_user'] = [
        'id' => 999,
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'role' => 'admin',
        'profile_image' => '',
        'phone' => '************',
        'address' => 'Test Address'
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test Logout Functionality</title>
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
    <link href="./assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fa-solid fa-vial me-2"></i>Logout Functionality Test</h4>
                    </div>
                    <div class="card-body">
                        <h5>Current Session Status:</h5>
                        
                        <?php if (isset($_SESSION['auth_user'])): ?>
                            <div class="alert alert-success">
                                <h6><i class="fa-solid fa-check-circle me-2"></i>User is logged in</h6>
                                <ul class="mb-0">
                                    <li><strong>User ID:</strong> <?php echo $_SESSION['auth_user']['id']; ?></li>
                                    <li><strong>Name:</strong> <?php echo $_SESSION['auth_user']['name']; ?></li>
                                    <li><strong>Email:</strong> <?php echo $_SESSION['auth_user']['email']; ?></li>
                                    <li><strong>Role:</strong> <?php echo $_SESSION['auth_user']['role']; ?></li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <h6><i class="fa-solid fa-exclamation-triangle me-2"></i>No user session found</h6>
                                <p class="mb-0">User is not logged in or session has been destroyed.</p>
                            </div>
                        <?php endif; ?>

                        <h5 class="mt-4">Test Actions:</h5>
                        <div class="d-grid gap-2">
                            <?php if (isset($_SESSION['auth_user'])): ?>
                                <a href="logout.php" class="btn btn-danger">
                                    <i class="fa-solid fa-right-from-bracket me-2"></i>Test Logout
                                </a>
                                <a href="index.php" class="btn btn-primary">
                                    <i class="fa-solid fa-home me-2"></i>Go to Dashboard
                                </a>
                            <?php else: ?>
                                <a href="login.php" class="btn btn-success">
                                    <i class="fa-solid fa-right-to-bracket me-2"></i>Go to Login
                                </a>
                            <?php endif; ?>
                            
                            <a href="test_logout.php" class="btn btn-secondary">
                                <i class="fa-solid fa-refresh me-2"></i>Refresh Page
                            </a>
                        </div>

                        <div class="mt-4">
                            <h6>Session Data Debug:</h6>
                            <pre class="bg-light p-3 rounded"><code><?php print_r($_SESSION); ?></code></pre>
                        </div>

                        <div class="mt-4">
                            <h6>Expected Logout Behavior:</h6>
                            <ol>
                                <li>Click "Test Logout" button</li>
                                <li>Should redirect to login.php</li>
                                <li>Should show "You have been successfully logged out" message</li>
                                <li>Session should be completely destroyed</li>
                                <li>Trying to access protected pages should redirect to login</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>
