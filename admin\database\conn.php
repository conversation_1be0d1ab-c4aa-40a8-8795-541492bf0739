<?php
// Database credentials
$host = 'localhost'; 
// Replace with your database name
$db_name = 'baraaro';  
// Replace with your database username
$username = 'root'; 
// Replace with your database password
$password = ''; 
// Character set (optional, but recommended)
$charset = 'utf8mb4'; 

// Data Source Name (DSN)
// This string contains the information required to connect to the database.
$dsn = "mysql:host={$host};dbname={$db_name};charset={$charset}";

// PDO connection options (optional, but recommended)
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION, // Throw exceptions on errors
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,       // Fetch results as associative arrays
    PDO::ATTR_EMULATE_PREPARES   => false,                  // Disable emulation of prepared statements for security
];

try {
    // Create a new PDO instance
    $pdo = new PDO($dsn, $username, $password, $options);

    // If the connection is successful, you can uncomment the line below to confirm
    // echo "Connected successfully to the database '{$db_name}'!";


} catch (PDOException $e) {
    // Handle connection errors
    // It's good practice to log errors or display a user-friendly message
    // For security reasons, avoid displaying detailed error messages (like $e->getMessage()) in a production environment.
    error_log("Connection failed: " . $e->getMessage()); // Log the error
    die("Database connection failed. Please try again later."); // Generic message for the user
}

?>
