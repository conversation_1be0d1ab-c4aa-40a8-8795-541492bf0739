(function ($) {
    "use strict";

    // Spinner
    var spinner = function () {
        setTimeout(function () {
            if ($('#spinner').length > 0) {
                $('#spinner').removeClass('show');
            }
        }, 1);
    };
    spinner();


    // Initiate the wowjs
    new WOW().init();


    // Sticky Navbar
    $(window).scroll(function () {
        if ($(this).scrollTop() > 300) {
            $('.sticky-top').css('top', '0px');
        } else {
            $('.sticky-top').css('top', '-100px');
        }
    });


    // Dropdown on mouse hover
    const $dropdown = $(".dropdown");
    const $dropdownToggle = $(".dropdown-toggle");
    const $dropdownMenu = $(".dropdown-menu");
    const showClass = "show";

    $(window).on("load resize", function() {
        if (this.matchMedia("(min-width: 992px)").matches) {
            $dropdown.hover(
            function() {
                const $this = $(this);
                $this.addClass(showClass);
                $this.find($dropdownToggle).attr("aria-expanded", "true");
                $this.find($dropdownMenu).addClass(showClass);
            },
            function() {
                const $this = $(this);
                $this.removeClass(showClass);
                $this.find($dropdownToggle).attr("aria-expanded", "false");
                $this.find($dropdownMenu).removeClass(showClass);
            }
            );
        } else {
            $dropdown.off("mouseenter mouseleave");
        }
    });


    // Back to top button
    $(window).scroll(function () {
        if ($(this).scrollTop() > 300) {
            $('.back-to-top').fadeIn('slow');
        } else {
            $('.back-to-top').fadeOut('slow');
        }
    });
    $('.back-to-top').click(function () {
        $('html, body').animate({scrollTop: 0}, 1500, 'easeInOutExpo');
        return false;
    });


    // Header carousel
    $(".header-carousel").owlCarousel({
        autoplay: true,
        smartSpeed: 1500,
        items: 1,
        dots: false,
        loop: true,
        nav : true,
        navText : [
            '<i class="bi bi-chevron-left"></i>',
            '<i class="bi bi-chevron-right"></i>'
        ]
    });


    // Testimonials carousel
    $(".testimonial-carousel").owlCarousel({
        autoplay: true,
        smartSpeed: 1000,
        center: true,
        margin: 24,
        dots: true,
        loop: true,
        nav : false,
        responsive: {
            0:{
                items:1
            },
            768:{
                items:2
            },
            992:{
                items:3
            }
        }
    });

    // Product carousel
    var productCarousel = $(".product-carousel").owlCarousel({
        autoplay: true,
        autoplayTimeout: 1500, // Changed from 3000 to 1500 (1.5 seconds)
        autoplayHoverPause: false, // Don't pause on hover
        autoplaySpeed: 800, // Slightly faster transition
        smartSpeed: 800, // Faster animations
        margin: 10,  // Margin between items
        dots: true,
        loop: true,
        rewind: false, // Ensure it doesn't stop at the end
        nav: true,
        navText: [
            '<i class="fa fa-angle-left"></i>',
            '<i class="fa fa-angle-right"></i>'
        ],
        responsive: {
            0:{
                items:1
            },
            576:{
                items:2
            },
            768:{
                items:3
            },
            992:{
                items:4
            }
        },
        onInitialized: fixOwlCarouselHeight
    });
    
    // Function to fix carousel height issues
    function fixOwlCarouselHeight(event) {
        var carousel = $(event.target);
        carousel.find('.owl-item').css('height', 'auto');
    }
    
    // Keep carousel running even when tab is inactive
    var carouselInterval;
    var lastTime = Date.now();
    var expectedInterval = 1500; // Should match autoplayTimeout above
    
    // Function to force carousel to next slide
    function forceNextSlide() {
        var now = Date.now();
        var deltaTime = now - lastTime;
        
        // If more time has passed than expected, the tab was likely inactive
        if (deltaTime > expectedInterval * 1.5) {
            // Calculate how many slides we should have moved
            var slidesToMove = Math.floor(deltaTime / expectedInterval);
            
            // Move carousel forward that many slides
            for (var i = 0; i < slidesToMove; i++) {
                productCarousel.trigger('next.owl.carousel');
            }
        }
        
        lastTime = now;
        productCarousel.trigger('next.owl.carousel');
    }
    
    // Start continuous timer that's more reliable across tab switches
    carouselInterval = setInterval(forceNextSlide, expectedInterval);
    
    // Page visibility API to detect tab switches
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            lastTime = Date.now();
        }
    });
    
    // Function to fix carousel height issues
    function fixOwlCarouselHeight(event) {
        var carousel = $(event.target);
        carousel.find('.owl-item').css('height', 'auto');
    }
    
    // Events Slider
    $(".events-slider").owlCarousel({
        autoplay: true,
        autoplayTimeout: 3000,
        smartSpeed: 1000,
        margin: 15,
        dots: true,
        loop: true,
        nav: true,
        navText: [
            '<i class="fa fa-angle-left"></i>',
            '<i class="fa fa-angle-right"></i>'
        ],
        responsive: {
            0: {
                items: 1
            },
            576: {
                items: 2
            },
            992: {
                items: 3
            }
        }
    });
    
    // Brand Logos Carousel
    $(".brand-logos-carousel").owlCarousel({
        autoplay: true,
        autoplayTimeout: 2000,
        autoplayHoverPause: false,
        smartSpeed: 600,
        margin: 20,
        dots: false,
        loop: true,
        responsive: {
            0: {
                items: 2
            },
            576: {
                items: 3
            },
            768: {
                items: 4
            },
            992: {
                items: 5
            },
            1200: {
                items: 6
            }
        }
    });

})(jQuery);

