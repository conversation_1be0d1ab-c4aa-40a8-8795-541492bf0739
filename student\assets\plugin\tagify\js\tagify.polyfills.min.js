/*
Tagify v4.32.1 - tags input component
By: Yair Even-Or <<EMAIL>>
https://github.com/yairEO/tagify

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

This Software may not be rebranded and sold as a library under any other name
other than "Tagify" (by owner) or as part of another library.
*/

!function(t){"function"==typeof define&&define.amd?define(t):t()}((function(){"use strict";function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),n}function e(t,e){return null!=e&&"undefined"!=typeof Symbol&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](t):t instanceof e}var n;"".trim||(String.prototype.trim=function(){return this.replace(/^[\s﻿]+|[\s﻿]+$/g,"")}),window.NodeList&&!NodeList.prototype.forEach&&(NodeList.prototype.forEach=Array.prototype.forEach),Array.prototype.findIndex||Object.defineProperty(Array.prototype,"findIndex",{value:function(t){if(null==this)throw new TypeError('"this" is null or not defined');var e=Object(this),n=e.length>>>0;if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var o=arguments[1],r=0;r<n;){var i=e[r];if(t.call(o,i,r,e))return r;r++}return-1},configurable:!0,writable:!0}),Array.prototype.includes||(Array.prototype.includes=function(t){return!!~this.indexOf(t)}),Array.prototype.some||(Array.prototype.some=function(t,e){if(null==this)throw new TypeError("Array.prototype.some called on null or undefined");if("function"!=typeof t)throw new TypeError;for(var n=Object(this),o=n.length>>>0,r=0;r<o;r++)if(r in n&&t.call(e,n[r],r,n))return!0;return!1}),String.prototype.includes||(String.prototype.includes=function(t,e){return"number"!=typeof e&&(e=0),!(e+t.length>this.length)&&-1!==this.indexOf(t,e)}),"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(t),o=1;o<arguments.length;o++){var r=arguments[o];if(null!=r)for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[i]=r[i])}return n},writable:!0,configurable:!0}),t.prototype=window.Event.prototype,"function"!=typeof window.CustomEvent&&(window.CustomEvent=t),Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(t){var e=this;if(!document.documentElement.contains(e))return null;do{if(e.matches(t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}),document.execCommand("AutoUrlDetect",!1,!1),/*! @source http://purl.eligrey.com/github/classList.js/blob/master/classList.js */"document"in self&&((!("classList"in document.createElement("_"))||document.createElementNS&&!("classList"in document.createElementNS("http://www.w3.org/2000/svg","g")))&&function(t){if("Element"in t){var e="classList",n="prototype",o=t.Element[n],r=Object,i=String[n].trim||function(){return this.replace(/^\s+|\s+$/g,"")},c=Array[n].indexOf||function(t){for(var e=0,n=this.length;e<n;e++)if(e in this&&this[e]===t)return e;return-1},s=function(t,e){this.name=t,this.code=DOMException[t],this.message=e},l=function(t,e){if(""===e)throw new s("SYNTAX_ERR","The token must not be empty.");if(/\s/.test(e))throw new s("INVALID_CHARACTER_ERR","The token must not contain space characters.");return c.call(t,e)},u=function(t){for(var e=i.call(t.getAttribute("class")||""),n=e?e.split(/\s+/):[],o=0,r=n.length;o<r;o++)this.push(n[o]);this._updateClassName=function(){t.setAttribute("class",this.toString())}},a=u[n]=[],f=function(){return new u(this)};if(s[n]=Error[n],a.item=function(t){return this[t]||null},a.contains=function(t){return~l(this,t+"")},a.add=function(){var t,e=arguments,n=0,o=e.length,r=!1;do{~l(this,t=e[n]+"")||(this.push(t),r=!0)}while(++n<o);r&&this._updateClassName()},a.remove=function(){var t,e,n=arguments,o=0,r=n.length,i=!1;do{for(e=l(this,t=n[o]+"");~e;)this.splice(e,1),i=!0,e=l(this,t)}while(++o<r);i&&this._updateClassName()},a.toggle=function(t,e){var n=this.contains(t),o=n?!0!==e&&"remove":!1!==e&&"add";return o&&this[o](t),!0===e||!1===e?e:!n},a.replace=function(t,e){var n=l(t+"");~n&&(this.splice(n,1,e),this._updateClassName())},a.toString=function(){return this.join(" ")},r.defineProperty){var p={get:f,enumerable:!0,configurable:!0};try{r.defineProperty(o,e,p)}catch(t){void 0!==t.number&&-2146823252!==t.number||(p.enumerable=!1,r.defineProperty(o,e,p))}}else r[n].__defineGetter__&&o.__defineGetter__(e,f)}}(self),function(){var t=document.createElement("_");if(t.classList.add("c1","c2"),!t.classList.contains("c2")){var e=function(t){var e=DOMTokenList.prototype[t];DOMTokenList.prototype[t]=function(t){var n,o=arguments.length;for(n=0;n<o;n++)t=arguments[n],e.call(this,t)}};e("add"),e("remove")}if(t.classList.toggle("c3",!1),t.classList.contains("c3")){var n=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(t,e){return 1 in arguments&&!this.contains(t)==!e?e:n.call(this,t)}}"replace"in document.createElement("_").classList||(DOMTokenList.prototype.replace=function(t,e){var n=this.toString().split(" "),o=n.indexOf(t+"");~o&&(n=n.slice(o),this.remove.apply(this,n),this.add(e),this.add.apply(this,n.slice(1)))}),t=null}()),globalThis,n=function(){function t(t){var e=this.constructor;return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){return e.reject(n)}))}))}function n(t){return new this((function(e,n){if(!t||void 0===t.length)return n(new TypeError((void 0===t?"undefined":(o=t)&&"undefined"!=typeof Symbol&&o.constructor===Symbol?"symbol":typeof o)+" "+t+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var o,r=Array.prototype.slice.call(t);if(0===r.length)return e([]);var i=r.length;function c(t,n){if(n&&("object"==typeof n||"function"==typeof n)){var o=n.then;if("function"==typeof o)return void o.call(n,(function(e){c(t,e)}),(function(n){r[t]={status:"rejected",reason:n},0==--i&&e(r)}))}r[t]={status:"fulfilled",value:n},0==--i&&e(r)}for(var s=0;s<r.length;s++)c(s,r[s])}))}var o=setTimeout;function r(t){return Boolean(t&&void 0!==t.length)}function i(){}function c(t){if(!e(this,c))throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],p(t,this)}function s(t,e){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,c._immediateFn((function(){var n=1===t._state?e.onFulfilled:e.onRejected;if(null!==n){var o;try{o=n(t._value)}catch(t){return void u(e.promise,t)}l(e.promise,o)}else(1===t._state?l:u)(e.promise,t._value)}))):t._deferreds.push(e)}function l(t,n){try{if(n===t)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var o=n.then;if(e(n,c))return t._state=3,t._value=n,void a(t);if("function"==typeof o)return void p((r=o,i=n,function(){r.apply(i,arguments)}),t)}t._state=1,t._value=n,a(t)}catch(e){u(t,e)}var r,i}function u(t,e){t._state=2,t._value=e,a(t)}function a(t){2===t._state&&0===t._deferreds.length&&c._immediateFn((function(){t._handled||c._unhandledRejectionFn(t._value)}));for(var e=0,n=t._deferreds.length;e<n;e++)s(t,t._deferreds[e]);t._deferreds=null}function f(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=n}function p(t,e){var n=!1;try{t((function(t){n||(n=!0,l(e,t))}),(function(t){n||(n=!0,u(e,t))}))}catch(t){if(n)return;n=!0,u(e,t)}}c.prototype.catch=function(t){return this.then(null,t)},c.prototype.then=function(t,e){var n=new this.constructor(i);return s(this,new f(t,e,n)),n},c.prototype.finally=t,c.all=function(t){return new c((function(e,n){if(!r(t))return n(new TypeError("Promise.all accepts an array"));var o=Array.prototype.slice.call(t);if(0===o.length)return e([]);var i=o.length;function c(t,r){try{if(r&&("object"==typeof r||"function"==typeof r)){var s=r.then;if("function"==typeof s)return void s.call(r,(function(e){c(t,e)}),n)}o[t]=r,0==--i&&e(o)}catch(t){n(t)}}for(var s=0;s<o.length;s++)c(s,o[s])}))},c.allSettled=n,c.resolve=function(t){return t&&"object"==typeof t&&t.constructor===c?t:new c((function(e){e(t)}))},c.reject=function(t){return new c((function(e,n){n(t)}))},c.race=function(t){return new c((function(e,n){if(!r(t))return n(new TypeError("Promise.race accepts an array"));for(var o=0,i=t.length;o<i;o++)c.resolve(t[o]).then(e,n)}))},c._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){o(t,0)},c._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)};var d=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();"function"!=typeof d.Promise?d.Promise=c:d.Promise.prototype.finally?d.Promise.allSettled||(d.Promise.allSettled=n):d.Promise.prototype.finally=t},"object"==typeof exports&&"undefined"!=typeof module?n():"function"==typeof define&&define.amd?define(n):n()}));
//# sourceMappingURL=tagify.polyfills.min.js.map
