<?php
/**
 * Image Helper Functions for Baraaro E-Learning Admin Dashboard
 * 
 * This file contains helper functions for handling profile images
 * and other image-related operations.
 */

/**
 * Get the full URL for a profile image
 * 
 * @param string|null $image_path The relative path stored in database
 * @param string $default_image Default image if no profile image exists
 * @return string Full URL to the image
 */
function getProfileImageUrl($image_path, $default_image = 'assets/images/profile.png') {
    if (empty($image_path) || !file_exists($image_path)) {
        return $default_image;
    }
    return $image_path;
}

/**
 * Display profile image with proper HTML
 * 
 * @param string|null $image_path The relative path stored in database
 * @param string $alt_text Alt text for the image
 * @param string $css_class CSS classes for the image
 * @param int $width Image width
 * @param int $height Image height
 * @return string HTML img tag
 */
function displayProfileImage($image_path, $alt_text = 'Profile Image', $css_class = 'profile-img', $width = 50, $height = 50) {
    $image_url = getProfileImageUrl($image_path);
    return sprintf(
        '<img src="%s" alt="%s" class="%s" width="%d" height="%d" style="object-fit: cover; border-radius: 50%%;">',
        htmlspecialchars($image_url),
        htmlspecialchars($alt_text),
        htmlspecialchars($css_class),
        $width,
        $height
    );
}

/**
 * Resize an image to specified dimensions (optional function)
 * 
 * @param string $source_path Path to source image
 * @param int $max_width Maximum width
 * @param int $max_height Maximum height
 * @return bool Success status
 */
function resizeImage($source_path, $max_width = 300, $max_height = 300) {
    if (!file_exists($source_path)) {
        return false;
    }
    
    $image_info = getimagesize($source_path);
    if (!$image_info) {
        return false;
    }
    
    list($width, $height, $type) = $image_info;
    
    // Calculate new dimensions
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = intval($width * $ratio);
    $new_height = intval($height * $ratio);
    
    // Create image resource based on type
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($source_path);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($source_path);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($source_path);
            break;
        case IMAGETYPE_WEBP:
            $source = imagecreatefromwebp($source_path);
            break;
        default:
            return false;
    }
    
    if (!$source) {
        return false;
    }
    
    // Create new image
    $destination = imagecreatetruecolor($new_width, $new_height);
    
    // Preserve transparency for PNG and GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($destination, false);
        imagesavealpha($destination, true);
        $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
        imagefilledrectangle($destination, 0, 0, $new_width, $new_height, $transparent);
    }
    
    // Resize image
    imagecopyresampled($destination, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
    
    // Save resized image
    $result = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($destination, $source_path, 85);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($destination, $source_path, 6);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($destination, $source_path);
            break;
        case IMAGETYPE_WEBP:
            $result = imagewebp($destination, $source_path, 85);
            break;
    }
    
    // Clean up memory
    imagedestroy($source);
    imagedestroy($destination);
    
    return $result;
}

/**
 * Delete a profile image file
 * 
 * @param string $image_path Path to the image file
 * @return bool Success status
 */
function deleteProfileImage($image_path) {
    if (empty($image_path) || !file_exists($image_path)) {
        return false;
    }
    
    // Don't delete default images
    if (strpos($image_path, 'assets/images/') === 0) {
        return false;
    }
    
    return unlink($image_path);
}

/**
 * Get file size in human readable format
 * 
 * @param string $file_path Path to the file
 * @return string Human readable file size
 */
function getFileSize($file_path) {
    if (!file_exists($file_path)) {
        return 'File not found';
    }
    
    $size = filesize($file_path);
    $units = ['B', 'KB', 'MB', 'GB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, 2) . ' ' . $units[$i];
}

/**
 * Validate uploaded image file
 * 
 * @param array $file $_FILES array element
 * @param int $max_size Maximum file size in bytes
 * @param array $allowed_types Allowed file extensions
 * @return array Array with 'success' boolean and 'message' string
 */
function validateImageUpload($file, $max_size = 2097152, $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp']) {
    $result = ['success' => false, 'message' => ''];
    
    // Check if file was uploaded
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        $result['message'] = 'No file uploaded or upload error occurred';
        return $result;
    }
    
    // Check file size
    if ($file['size'] > $max_size) {
        $result['message'] = 'File size exceeds maximum allowed size (' . ($max_size / 1024 / 1024) . 'MB)';
        return $result;
    }
    
    // Check file extension
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        $result['message'] = 'File type not allowed. Allowed types: ' . implode(', ', $allowed_types);
        return $result;
    }
    
    // Check if it's actually an image
    if (!getimagesize($file['tmp_name'])) {
        $result['message'] = 'Invalid image file';
        return $result;
    }
    
    $result['success'] = true;
    $result['message'] = 'File validation passed';
    return $result;
}

?>

<!-- Example usage in HTML -->
<!--
<?php
// Include the helper
require_once 'helpers/image_helper.php';

// Display a user's profile image
$user_image_path = 'uploads/profiles/profile_12345.jpg'; // From database
echo displayProfileImage($user_image_path, 'John Doe Profile', 'rounded-circle', 100, 100);

// Get image URL for use in HTML
$image_url = getProfileImageUrl($user_image_path);
echo '<img src="' . $image_url . '" alt="Profile">';
?>
-->
