<?php
/**
 * Verify Logout Functionality
 * 
 * This script verifies that the logout system is working correctly
 */

session_start();

// Check if user is logged in
$isLoggedIn = isset($_SESSION['auth_user']) && !empty($_SESSION['auth_user']['id']);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Verify Logout</title>
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
    <style>
        .status-card { border-left: 4px solid #28a745; }
        .error-card { border-left: 4px solid #dc3545; }
        .warning-card { border-left: 4px solid #ffc107; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fa-solid fa-check-circle me-2"></i>Logout System Verification</h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Current Status -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card <?php echo $isLoggedIn ? 'status-card' : 'warning-card'; ?>">
                                    <div class="card-body">
                                        <h6><i class="fa-solid fa-user me-2"></i>Session Status</h6>
                                        <?php if ($isLoggedIn): ?>
                                            <p class="text-success mb-0">✅ User is logged in</p>
                                            <small>User: <?php echo htmlspecialchars($_SESSION['auth_user']['name']); ?></small>
                                        <?php else: ?>
                                            <p class="text-warning mb-0">⚠️ No active session</p>
                                            <small>User is not logged in</small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card status-card">
                                    <div class="card-body">
                                        <h6><i class="fa-solid fa-file me-2"></i>Logout File</h6>
                                        <?php if (file_exists('logout.php')): ?>
                                            <p class="text-success mb-0">✅ logout.php exists</p>
                                            <small>File size: <?php echo filesize('logout.php'); ?> bytes</small>
                                        <?php else: ?>
                                            <p class="text-danger mb-0">❌ logout.php missing</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Test Actions -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Test Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <?php if (!$isLoggedIn): ?>
                                            <button onclick="createTestSession()" class="btn btn-success w-100">
                                                <i class="fa-solid fa-plus me-2"></i>Create Test Session
                                            </button>
                                        <?php else: ?>
                                            <a href="logout.php" class="btn btn-danger w-100">
                                                <i class="fa-solid fa-right-from-bracket me-2"></i>Test Logout
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a href="login.php" class="btn btn-primary w-100">
                                            <i class="fa-solid fa-right-to-bracket me-2"></i>Go to Login
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a href="verify_logout.php" class="btn btn-secondary w-100">
                                            <i class="fa-solid fa-refresh me-2"></i>Refresh
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Logout Links Check -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Logout Links Verification</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Include Files:</h6>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>top-navbar.php</span>
                                                <?php
                                                $topNavbar = file_get_contents('include/top-navbar.php');
                                                $hasCorrectLink = strpos($topNavbar, 'href="logout.php"') !== false;
                                                echo $hasCorrectLink ? '<span class="text-success">✅</span>' : '<span class="text-danger">❌</span>';
                                                ?>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>sidebar.php</span>
                                                <?php
                                                $sidebar = file_get_contents('include/sidebar.php');
                                                $hasCorrectLink = strpos($sidebar, 'href="logout.php"') !== false;
                                                echo $hasCorrectLink ? '<span class="text-success">✅</span>' : '<span class="text-danger">❌</span>';
                                                ?>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Direct Test Links:</h6>
                                        <div class="d-grid gap-2">
                                            <a href="logout.php" class="btn btn-outline-danger btn-sm">Direct logout.php</a>
                                            <a href="include/top-navbar.php" class="btn btn-outline-info btn-sm">View top-navbar.php</a>
                                            <a href="include/sidebar.php" class="btn btn-outline-info btn-sm">View sidebar.php</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Session Debug -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Session Debug Information</h5>
                            </div>
                            <div class="card-body">
                                <pre class="bg-light p-3 rounded"><code><?php print_r($_SESSION); ?></code></pre>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./assets/js/bootstrap.bundle.min.js"></script>
    <script>
        function createTestSession() {
            // Create a test session via AJAX
            fetch('verify_logout.php?action=create_session', {
                method: 'POST'
            }).then(() => {
                location.reload();
            });
        }
    </script>
</body>
</html>

<?php
// Handle AJAX request to create test session
if (isset($_GET['action']) && $_GET['action'] === 'create_session') {
    $_SESSION['auth_user'] = [
        'id' => 999,
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'role' => 'admin',
        'profile_image' => '',
        'phone' => '************',
        'address' => 'Test Address'
    ];
    echo 'Session created';
    exit;
}
?>
