/* Upcoming Events Section Styling */
.events-section {
    padding: 0;
    margin-top: -20px;
    position: relative;
    overflow: hidden;
    background: transparent;
}

.events-section::before {
    content: '';
    position: absolute;
    top: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(29, 191, 113, 0.1);
    z-index: 0;
}

.events-section::after {
    content: '';
    position: absolute;
    bottom: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(29, 191, 113, 0.1);
    z-index: 0;
}

.section-title {
    position: relative;
    z-index: 1;
}

.events-slider {
    position: relative;
    padding: 20px 10px;
    z-index: 1;
}

.event-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 15px;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.event-card .event-date {
    background: #1dbf71;
    color: #fff;
    text-align: center;
    padding: 12px 10px;
    border-radius: 0 0 20px 20px;
    font-weight: 700;
    position: absolute;
    top: 0;
    left: 30px;
    z-index: 2;
    min-width: 80px;
    box-shadow: 0 5px 15px rgba(29, 191, 113, 0.3);
    transition: all 0.3s ease;
}

.event-card:hover .event-date {
    padding-top: 18px;
}

.event-card .event-date .day {
    font-size: 24px;
    line-height: 1;
    margin-bottom: 3px;
}

.event-card .event-date .month {
    font-size: 14px;
    text-transform: uppercase;
}

.event-card .event-img {
    height: 180px;
    overflow: hidden;
    position: relative;
}

.event-card .event-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.event-card:hover .event-img img {
    transform: scale(1.1);
}

.event-card .event-content {
    padding: 20px;
    position: relative;
}

.event-card .event-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #333;
    transition: all 0.3s ease;
}

.event-card:hover .event-title {
    color: #1dbf71;
}

.event-info {
    margin-bottom: 15px;
}

.event-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #666;
    font-size: 14px;
}

.event-info-item i {
    color: #1dbf71;
    margin-right: 10px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.event-seats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px dashed #e1e1e1;
}

.event-seats .seats-left {
    background: #f8f9fa;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 13px;
    color: #333;
    font-weight: 600;
}

.event-seats .limited-seats {
    color: #ff6b6b;
}

.event-card .btn-details {
    background: transparent;
    color: #1dbf71;
    border: 2px solid #1dbf71;
    border-radius: 30px;
    padding: 5px 15px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.event-card .btn-details:hover {
    background: #1dbf71;
    color: #fff;
}

.event-tag {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #ff6b6b;
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 20px;
    z-index: 2;
}

.online-tag {
    background: #4a89dc;
}

.free-tag {
    background: #8cc152;
}

.premium-tag {
    background: #f6bb42;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
    .events-slider {
        padding: 10px 0;
    }
    
    .event-card {
        margin: 10px;
    }
}

/* Slider Navigation */
.events-slider .owl-nav {
    text-align: center;
    margin-top: 20px;
}

.events-slider .owl-nav button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #fff !important;
    color: #1dbf71 !important;
    margin: 0 5px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.events-slider .owl-nav button:hover {
    background: #1dbf71 !important;
    color: #fff !important;
}

.events-slider .owl-dots {
    text-align: center;
    margin-top: 15px;
}

.events-slider .owl-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ccc !important;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.events-slider .owl-dot.active {
    background: #1dbf71 !important;
    width: 20px;
    border-radius: 10px;
}

/* Small Event Date Styling */
.event-date-sm {
    padding: 8px 15px;
    min-width: 70px;
}

.event-date-sm p {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.2;
}

.event-date-sm h6 {
    font-size: 14px;
    text-transform: uppercase;
    line-height: 1.2;
}

/* Calendar Event Day Styles */
.calendar-date {
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-date:hover {
    background-color: rgba(34, 139, 34, 0.1);
}

.calendar-date.active {
    background-color: rgba(34, 139, 34, 0.2);
    font-weight: 600;
}

#calendarEventDetails {
    border-left: 4px solid var(--primary);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.5s ease;
}

/* Counter section styles */
.counter {
    font-size: 36px;
    font-weight: 700;
    color: var(--primary);
}

/* Stats animation */
.fadeIn {
    animation-duration: 1.5s;
}

.events-title {
    display: inline-block;
    background: linear-gradient(135deg, #ff6b6b, #ff922b);
    color: white;
    padding: 8px 25px;
    border-radius: 30px;
    font-size: 24px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
}

.events-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* View All Button */
.view-all-events {
    text-align: center;
    margin-top: 10px;
}

.btn-view-all {
    background: #1dbf71;
    color: #fff;
    border-radius: 30px;
    padding: 10px 25px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(29, 191, 113, 0.3);
    transition: all 0.3s ease;
}

.btn-view-all:hover {
    background: #169c5b;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(29, 191, 113, 0.4);
}
