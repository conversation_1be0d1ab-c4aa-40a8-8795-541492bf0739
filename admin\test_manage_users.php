<?php
// Simple test page to verify manage users functionality
require_once 'database/conn.php';

// Create database instance
class SimpleDB {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
}

$db = new SimpleDB($pdo);

try {
    $users = $db->fetchAll("SELECT id, name, email, user_role, profile_image, status, created_at FROM users ORDER BY id");
    $user_count = count($users);
} catch (Exception $e) {
    $users = [];
    $user_count = 0;
    $error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test Manage Users | Baraaro E-Learning</title>
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
    <style>
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-suspended { background: #fff3cd; color: #856404; }
    </style>
</head>
<body style="background: #f5f5f5;">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fa-solid fa-users me-2"></i>Test Manage Users Functionality</h2>
                    <div>
                        <a href="manage-users.php" class="btn btn-primary">
                            <i class="fa-solid fa-cog me-2"></i>Go to Manage Users
                        </a>
                        <a href="create-user.php" class="btn btn-success">
                            <i class="fa-solid fa-user-plus me-2"></i>Create User
                        </a>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fa-solid fa-database me-2"></i>
                            Database Status: <?php echo $user_count; ?> Users Found
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                <strong>Database Error:</strong> <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php elseif (empty($users)): ?>
                            <div class="text-center py-5">
                                <i class="fa-solid fa-user-slash fa-3x text-muted mb-3"></i>
                                <h5>No users found</h5>
                                <p class="text-muted">The users table is empty. Create some users to test the manage functionality.</p>
                                <a href="create-user.php" class="btn btn-primary">Create First User</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Profile Image</th>
                                            <th>Created</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td><?php echo $user['id']; ?></td>
                                                <td><?php echo htmlspecialchars($user['name']); ?></td>
                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                <td>
                                                    <span class="badge bg-primary">
                                                        <?php echo ucwords(str_replace('_', ' ', $user['user_role'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $user['status']; ?>">
                                                        <?php echo ucfirst($user['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if (!empty($user['profile_image'])): ?>
                                                        <?php if (file_exists($user['profile_image'])): ?>
                                                            <span class="text-success">
                                                                <i class="fa-solid fa-check-circle"></i> 
                                                                <?php echo basename($user['profile_image']); ?>
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="text-danger">
                                                                <i class="fa-solid fa-exclamation-triangle"></i> 
                                                                Missing: <?php echo basename($user['profile_image']); ?>
                                                            </span>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">
                                                            <i class="fa-solid fa-minus"></i> No image
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M j, Y H:i', strtotime($user['created_at'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fa-solid fa-list-check me-2"></i>
                            Functionality Test Checklist
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>✅ Implemented Features:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fa-solid fa-check text-success me-2"></i>Database connection</li>
                                    <li><i class="fa-solid fa-check text-success me-2"></i>Fetch users from database</li>
                                    <li><i class="fa-solid fa-check text-success me-2"></i>Display user data dynamically</li>
                                    <li><i class="fa-solid fa-check text-success me-2"></i>Profile image path handling</li>
                                    <li><i class="fa-solid fa-check text-success me-2"></i>Role badge styling</li>
                                    <li><i class="fa-solid fa-check text-success me-2"></i>Delete single user</li>
                                    <li><i class="fa-solid fa-check text-success me-2"></i>Bulk delete users</li>
                                    <li><i class="fa-solid fa-check text-success me-2"></i>Success/error messages</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>🔄 To Test:</h6>
                                <ol>
                                    <li>Go to <a href="manage-users.php">manage-users.php</a></li>
                                    <li>Verify users are displayed from database</li>
                                    <li>Test single user deletion</li>
                                    <li>Test bulk user deletion</li>
                                    <li>Check profile images display correctly</li>
                                    <li>Verify DataTable functionality works</li>
                                </ol>
                                
                                <h6 class="mt-3">🚧 Future Enhancements:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fa-solid fa-clock text-warning me-2"></i>Edit user functionality</li>
                                    <li><i class="fa-solid fa-clock text-warning me-2"></i>View user details modal</li>
                                    <li><i class="fa-solid fa-clock text-warning me-2"></i>Bulk status update</li>
                                    <li><i class="fa-solid fa-clock text-warning me-2"></i>User search and filtering</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="./assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>
