-- Sliders table structure for homepage slider management
-- This table stores slider information with image file paths

CREATE TABLE IF NOT EXISTS `sliders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subtitle` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `image` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Sample data (optional)
-- INSERT INTO `sliders` (`subtitle`, `title`, `description`, `image`) VALUES
-- ('Welcome to Our Platform', 'Learn & Grow Together', 'Discover amazing courses and products that will help you achieve your goals.', 'uploads/sliders/sample1.jpg'),
-- ('New Arrivals', 'Fresh Content Weekly', 'Stay updated with our latest courses and product releases every week.', 'uploads/sliders/sample2.jpg'),
-- ('Join Our Community', 'Connect & Learn', 'Be part of our growing community of learners and professionals.', 'uploads/sliders/sample3.jpg');
