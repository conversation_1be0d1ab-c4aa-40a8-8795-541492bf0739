/* Brands Section Styling */
.brands-section {
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Brand Title Styling */
.brands-title-wrapper {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.brands-title {
    display: inline-block;
    background: linear-gradient(135deg, #06BBCC, #0dcaf0);
    color: white;
    padding: 8px 25px;
    border-radius: 30px;
    font-size: 24px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(6, 187, 204, 0.3);
    position: relative;
    overflow: hidden;
}

.brands-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
    animation: shimmer 2s infinite;
}

/* Brand Logo Items */
.brand-logos-carousel {
    padding: 0 30px;
}

.brand-logo-item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
    margin: 0 10px;
    height: 100px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.brand-logo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.brand-logo-item img {
    max-width: 100%;
    max-height: 70px;
    filter: grayscale(100%);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.brand-logo-item:hover img {
    filter: grayscale(0%);
    opacity: 1;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
    .brand-logo-item {
        height: 80px;
    }
    
    .brand-logo-item img {
        max-height: 50px;
    }
}
