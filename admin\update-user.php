<?php
// Start session for flash messages
session_start();

// Include database connection
require_once 'database/conn.php';

// Create a simple database wrapper class for this file
class SimpleDB {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function fetchRow($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }

    public function update($table, $data, $where, $params = []) {
        $set = implode(', ', array_map(function($key) {
            return "{$key} = :{$key}";
        }, array_keys($data)));
        
        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);
        
        // Merge data and where params
        $allParams = array_merge($data, $params);
        return $stmt->execute($allParams);
    }
}

// Create database instance
$db = new SimpleDB($pdo);

// Initialize response array
$response = [
    'success' => false,
    'message' => 'No action taken'
];

// Handle user update via AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['user_id'])) {
    try {
        // Get user ID and validate it's an integer
        $user_id = (int)$_POST['user_id'];
        
        // Check if user exists
        $existing_user = $db->fetchRow("SELECT id, email, profile_image FROM users WHERE id = ?", [$user_id]);
        if (!$existing_user) {
            $response['message'] = "User not found";
            echo json_encode($response);
            exit;
        }
        
        // Validate and sanitize input data
        $name = trim(htmlspecialchars($_POST['name'] ?? '', ENT_QUOTES, 'UTF-8'));
        $phone = trim(htmlspecialchars($_POST['phone'] ?? '', ENT_QUOTES, 'UTF-8'));
        $email = filter_var($_POST['email'] ?? '', FILTER_SANITIZE_EMAIL);
        $user_role = trim(htmlspecialchars($_POST['user_role'] ?? '', ENT_QUOTES, 'UTF-8'));
        $address = trim(htmlspecialchars($_POST['address'] ?? '', ENT_QUOTES, 'UTF-8'));
        
        // Validation
        $errors = [];

        if (empty($name)) $errors[] = "Full name is required";
        if (empty($phone)) $errors[] = "Phone number is required";
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = "Valid email is required";
        if (empty($user_role)) $errors[] = "User role is required";
        
        // Check if email already exists (for another user)
        if ($email !== $existing_user['email']) {
            $email_check = $db->fetchRow("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $user_id]);
            if ($email_check) {
                $errors[] = "Email address already exists for another user";
            }
        }
        
        // Handle file upload if a new image is provided
        $profile_image_path = $existing_user['profile_image']; // Keep existing by default
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/profiles/';

            // Create upload directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            // Validate file type
            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = "Only JPG, JPEG, PNG, GIF, and WebP files are allowed";
            }
            // Validate file size (2MB limit)
            elseif ($_FILES['profile_image']['size'] > 2 * 1024 * 1024) {
                $errors[] = "File size must be less than 2MB";
            }
            // Validate actual image file (security check)
            elseif (!getimagesize($_FILES['profile_image']['tmp_name'])) {
                $errors[] = "Invalid image file";
            }
            else {
                // Generate unique filename to prevent conflicts
                $unique_filename = uniqid('profile_', true) . '.' . $file_extension;
                $upload_path = $upload_dir . $unique_filename;

                // Move uploaded file to destination
                if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                    // Store only the relative path in database (not the full system path)
                    $profile_image_path = $upload_path;
                } else {
                    $errors[] = "Failed to upload profile image";
                }
            }
        }
        
        // If no errors, update user
        if (empty($errors)) {
            $user_data = [
                'name' => $name,
                'phone' => $phone,
                'email' => $email,
                'user_role' => $user_role,
                'address' => $address,
            ];
            
            // Only update profile_image if a new one was uploaded
            if ($profile_image_path !== $existing_user['profile_image']) {
                $user_data['profile_image'] = $profile_image_path;
            }
            
            $success = $db->update('users', $user_data, 'id = :id', ['id' => $user_id]);
            
            if ($success) {
                $response['success'] = true;
                $response['message'] = "User updated successfully";
                
                // Store success message in session for page reload
                $_SESSION['success_message'] = "User has been updated successfully.";
            } else {
                $response['message'] = "Failed to update user. Please try again.";
            }
        } else {
            $response['message'] = implode("<br>", $errors);
        }
        
    } catch (Exception $e) {
        error_log("User update error: " . $e->getMessage());
        $response['message'] = "An error occurred while updating the user: " . $e->getMessage();
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);