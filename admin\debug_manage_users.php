<?php
// Debug script to troubleshoot manage-users.php issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Debugging Manage Users Issues</h2>";
echo "<hr>";

// Step 1: Check database connection
echo "<h3>1. Database Connection Test</h3>";
try {
    require_once 'database/conn.php';
    echo "✅ Database connection file loaded successfully<br>";
    
    if (isset($pdo)) {
        echo "✅ PDO object exists<br>";
        
        // Test basic query
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        if ($result['test'] == 1) {
            echo "✅ Database connection is working<br>";
        }
    } else {
        echo "❌ PDO object not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Database connection error: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Step 2: Check if users table exists
echo "<h3>2. Users Table Check</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $table_exists = $stmt->rowCount() > 0;
    
    if ($table_exists) {
        echo "✅ Users table exists<br>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll();
        echo "📋 Table structure:<br>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
        
        // Count users
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $count = $stmt->fetch()['count'];
        echo "👥 Number of users in table: <strong>{$count}</strong><br>";
        
        if ($count > 0) {
            // Show sample users
            $stmt = $pdo->query("SELECT id, name, email, user_role FROM users LIMIT 3");
            $sample_users = $stmt->fetchAll();
            echo "📝 Sample users:<br>";
            echo "<ul>";
            foreach ($sample_users as $user) {
                echo "<li>ID: {$user['id']}, Name: {$user['name']}, Email: {$user['email']}, Role: {$user['user_role']}</li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "❌ Users table does not exist<br>";
        echo "💡 <strong>Solution:</strong> Run the database setup script: <a href='database/setup_simple.php'>database/setup_simple.php</a><br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking users table: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Step 3: Test SimpleDB class
echo "<h3>3. SimpleDB Class Test</h3>";
try {
    class SimpleDB {
        private $pdo;
        
        public function __construct($pdo) {
            $this->pdo = $pdo;
        }
        
        public function fetchAll($sql, $params = []) {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        }
    }
    
    $db = new SimpleDB($pdo);
    echo "✅ SimpleDB class created successfully<br>";
    
    // Test query
    $users = $db->fetchAll("SELECT id, name, email FROM users LIMIT 1");
    echo "✅ SimpleDB fetchAll method works<br>";
    echo "📊 Query result: " . count($users) . " users fetched<br>";
    
} catch (Exception $e) {
    echo "❌ SimpleDB error: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Step 4: Check helpers file
echo "<h3>4. Helper Functions Check</h3>";
if (file_exists('helpers/image_helper.php')) {
    echo "✅ helpers/image_helper.php file exists<br>";
    try {
        require_once 'helpers/image_helper.php';
        echo "✅ helpers/image_helper.php loaded successfully<br>";
        
        if (function_exists('getProfileImageUrl')) {
            echo "✅ getProfileImageUrl function exists<br>";
            
            // Test the function
            $test_url = getProfileImageUrl(null);
            echo "🧪 Test function call result: {$test_url}<br>";
        } else {
            echo "❌ getProfileImageUrl function not found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error loading helpers: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ helpers/image_helper.php file does not exist<br>";
    echo "💡 <strong>Solution:</strong> The helper file is missing. Let me create a simple version.<br>";
}

echo "<hr>";

// Step 5: Test the actual query from manage-users.php
echo "<h3>5. Test Actual Query</h3>";
try {
    $db = new SimpleDB($pdo);
    $users = $db->fetchAll("
        SELECT id, name, phone, email, user_role, profile_image, status, created_at, updated_at 
        FROM users 
        ORDER BY created_at DESC
    ");
    
    echo "✅ Query executed successfully<br>";
    echo "📊 Found " . count($users) . " users<br>";
    
    if (!empty($users)) {
        echo "📝 First user data:<br>";
        $first_user = $users[0];
        echo "<pre>";
        print_r($first_user);
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "❌ Query error: " . $e->getMessage() . "<br>";
    echo "🔍 Error details: " . $e->getTraceAsString() . "<br>";
}

echo "<hr>";

// Step 6: Quick fixes
echo "<h3>6. Quick Fixes</h3>";
echo "<p><strong>If you see errors above, here are the solutions:</strong></p>";
echo "<ul>";
echo "<li>❌ Database connection issues → Check database credentials in database/conn.php</li>";
echo "<li>❌ Users table missing → Run <a href='database/setup_simple.php'>database/setup_simple.php</a></li>";
echo "<li>❌ Helper functions missing → I'll create them below</li>";
echo "<li>❌ No users in table → Create users via <a href='create-user.php'>create-user.php</a></li>";
echo "</ul>";

// Create simple helper functions if missing
if (!function_exists('getProfileImageUrl')) {
    echo "<h4>Creating missing helper functions:</h4>";
    echo "<pre>";
    echo "function getProfileImageUrl(\$image_path, \$default_image = './assets/images/profile.png') {
    if (empty(\$image_path) || !file_exists(\$image_path)) {
        return \$default_image;
    }
    return \$image_path;
}";
    echo "</pre>";
}

echo "<hr>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Fix any issues shown above</li>";
echo "<li>Try <a href='manage-users.php'>manage-users.php</a> again</li>";
echo "<li>If still having issues, check the browser console for JavaScript errors</li>";
echo "</ol>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
hr { margin: 20px 0; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
ul, ol { margin-left: 20px; }
</style>
