$adminFiles = Get-ChildItem -Path 'admin' -Filter '*.html' | Where-Object { $_.Name -ne 'index.html' -and $_.Name -ne 'course.html' }

foreach ($file in $adminFiles) {
    Write-Host "Processing $($file.Name)..."
    
    # Read the file content
    $content = Get-Content $file.FullName -Raw
    
    # Fix CSS and JS paths
    $content = $content -replace 'href="assets/', 'href="./assets/'
    $content = $content -replace 'src="assets/', 'src="./assets/'
    
    # Write the updated content back to the file
    Set-Content $file.FullName $content
    
    Write-Host "Fixed $($file.Name)"
}

Write-Host "All files have been processed successfully!"
