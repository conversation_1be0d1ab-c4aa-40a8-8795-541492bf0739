<?php
// Ensure session is started. It's best practice for the parent script (e.g., index.php)
// to call session_start() at its very beginning. This is a fallback.
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include image helper for profile image handling
require_once 'helpers/image_helper.php';
?>
   <div class="header d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="collapse-sidebar me-3 d-none d-lg-block text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="menu-toggle me-3 d-block d-lg-none text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="d-none d-md-block d-lg-block">
                        <div class="input-group flex-nowrap">
                            <span class="input-group-text bg-white " id="addon-wrapping"><i class="fa-solid search-icon fa-magnifying-glass text-color-1"></i></span>
                            <input type="text" class="form-control search-input border-l-none ps-0" placeholder="Search anything" aria-label="Username" aria-describedby="addon-wrapping">
                        </div>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <ul class="nav d-flex align-items-center">
                        <!-- Messages Dropdown -->


                         <!-- User Profile -->
                        <li class="nav-item dropdown user-profile">
                            <div class="d-flex align-items-center dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="user-avatar me-0 me-lg-3">
                                    <?php
                                    // Get profile image path and create proper URL
                                    $profile_image_path = isset($_SESSION['auth_user']['profile_image']) ? $_SESSION['auth_user']['profile_image'] : '';
                                    $profile_image_url = '';

                                    if (!empty($profile_image_path)) {
                                        // Extract filename from path regardless of directory structure
                                        $filename = basename($profile_image_path);

                                        // Always use the uploads/profiles directory with the extracted filename
                                        $corrected_path = './uploads/profiles/' . $filename;

                                        // Check if file exists in corrected path
                                        if (file_exists($corrected_path)) {
                                            $profile_image_url = $corrected_path;
                                        }
                                        // If corrected path doesn't work, try the original path
                                        elseif (file_exists($profile_image_path)) {
                                            $profile_image_url = $profile_image_path;
                                        }
                                    }

                                    // Display image if we have a valid URL
                                    if (!empty($profile_image_url)): ?>
                                        <img src="<?php echo htmlspecialchars($profile_image_url); ?>" alt="User" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <!-- Fallback to letter avatar with proper styling -->
                                        <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                                             style="width: 40px; height: 40px; background-color: #6c757d; font-size: 16px;">
                                            <?php
                                            if (isset($_SESSION['auth_user']['name']) && !empty($_SESSION['auth_user']['name'])) {
                                                echo strtoupper(substr(htmlspecialchars($_SESSION['auth_user']['name']), 0, 1));
                                            } else {
                                                echo 'A'; // Default initial
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </span>
                                <div>
                                    <a href="#" class="d-none d-lg-block">
                                        <span class="d-block auth-role">
                                            <?php
                                                if (isset($_SESSION['auth_user']['role'])) {
                                                    echo htmlspecialchars(ucfirst(str_replace('_', ' ', $_SESSION['auth_user']['role'])));
                                                } else {
                                                    echo 'Guest'; // Default role
                                                }
                                            ?>
                                        </span>
                                        <span class="auth-name">
                                            <?php echo isset($_SESSION['auth_user']['name']) ? htmlspecialchars($_SESSION['auth_user']['name']) : 'Guest User'; // Default name ?>
                                        </span>
                                        <span class="ms-2 text-color-1 text-size-sm"><i class="fa-solid fa-angle-down"></i></span>
                                    </a>
                                    <ul class="dropdown-menu mt-3">
                                        <li><a class="dropdown-item" href="#">Profile</a></li>
                                        <li><a class="dropdown-item" href="#">Settings</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
