<?php
// Start session for flash messages
session_start();

// Include database connection
require_once 'database/conn.php';

// Create a simple database wrapper class for this file
class SimpleDB {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function fetchRow($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }

    public function update($table, $data, $where, $params = []) {
        $set = implode(', ', array_map(function($key) {
            return "{$key} = :{$key}";
        }, array_keys($data)));
        
        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);
        
        // Merge data and where params
        $allParams = array_merge($data, $params);
        return $stmt->execute($allParams);
    }
}

// Create database instance
$db = new SimpleDB($pdo);

// Initialize response array
$response = [
    'success' => false,
    'message' => 'No action taken'
];

// Handle event update via AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['event_id'])) {
    try {
        // Get event ID and validate it's an integer
        $event_id = (int)$_POST['event_id'];
        
        // Check if event exists
        $existing_event = $db->fetchRow("SELECT id, image FROM upcoming_events WHERE id = ?", [$event_id]);
        if (!$existing_event) {
            $response['message'] = "Event not found";
            echo json_encode($response);
            exit;
        }
        
        // Validate and sanitize input data
        $category = trim(htmlspecialchars($_POST['category'] ?? '', ENT_QUOTES, 'UTF-8'));
        $title = trim(htmlspecialchars($_POST['title'] ?? '', ENT_QUOTES, 'UTF-8'));
        $location = trim(htmlspecialchars($_POST['location'] ?? '', ENT_QUOTES, 'UTF-8'));
        $price = floatval($_POST['price'] ?? 0);
        $seats = intval($_POST['seats'] ?? 0);
        $event_date = $_POST['event_date'] ?? '';
        $event_starting_time = $_POST['event_starting_time'] ?? '';
        $event_ending_time = $_POST['event_ending_time'] ?? '';
        $host = trim(htmlspecialchars($_POST['host'] ?? '', ENT_QUOTES, 'UTF-8'));
        
        // Validation
        $errors = [];

        if (empty($category)) $errors[] = "Category is required";
        if (empty($title)) $errors[] = "Title is required";
        if (empty($location)) $errors[] = "Location is required";
        if ($price < 0) $errors[] = "Price must be a positive number";
        if ($seats <= 0) $errors[] = "Seats must be a positive number";
        if (empty($event_date)) $errors[] = "Event date is required";
        if (empty($event_starting_time)) $errors[] = "Event starting time is required";
        if (empty($event_ending_time)) $errors[] = "Event ending time is required";
        if (empty($host)) $errors[] = "Host is required";

        // Validate date format
        if (!empty($event_date)) {
            $date = DateTime::createFromFormat('Y-m-d', $event_date);
            if (!$date || $date->format('Y-m-d') !== $event_date) {
                $errors[] = "Invalid date format";
            }
        }

        // Validate time format and logic
        if (!empty($event_starting_time) && !empty($event_ending_time)) {
            if ($event_starting_time >= $event_ending_time) {
                $errors[] = "Event ending time must be after starting time";
            }
        }

        // Handle file upload if a new image is provided
        $image_path = $existing_event['image']; // Keep existing by default
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/events/';

            // Create upload directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            // Validate file type
            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = "Only JPG, JPEG, PNG, GIF, and WebP files are allowed";
            }
            // Validate file size (5MB limit)
            elseif ($_FILES['image']['size'] > 5 * 1024 * 1024) {
                $errors[] = "File size must be less than 5MB";
            }
            // Validate actual image file (security check)
            elseif (!getimagesize($_FILES['image']['tmp_name'])) {
                $errors[] = "Invalid image file";
            }
            else {
                // Generate unique filename to prevent conflicts
                $unique_filename = uniqid('event_', true) . '.' . $file_extension;
                $upload_path = $upload_dir . $unique_filename;

                // Move uploaded file to destination
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    // Delete old image file if it exists and is different
                    if ($existing_event['image'] && $existing_event['image'] !== $upload_path && file_exists($existing_event['image'])) {
                        unlink($existing_event['image']);
                    }
                    // Store only the relative path in database
                    $image_path = $upload_path;
                } else {
                    $errors[] = "Failed to upload event image";
                }
            }
        }
        
        // If no errors, update event
        if (empty($errors)) {
            $event_data = [
                'category' => $category,
                'title' => $title,
                'location' => $location,
                'price' => $price,
                'seats' => $seats,
                'image' => $image_path,
                'event_date' => $event_date,
                'event_starting_time' => $event_starting_time,
                'event_ending_time' => $event_ending_time,
                'host' => $host
            ];
            
            $success = $db->update('upcoming_events', $event_data, 'id = :id', ['id' => $event_id]);
            
            if ($success) {
                $response['success'] = true;
                $response['message'] = "Event updated successfully";
                
                // Store success message in session for page reload
                $_SESSION['success_message'] = "Event has been updated successfully.";
            } else {
                $response['message'] = "Failed to update event. Please try again.";
            }
        } else {
            $response['message'] = implode("<br>", $errors);
        }
        
    } catch (Exception $e) {
        error_log("Event update error: " . $e->getMessage());
        $response['message'] = "An error occurred while updating the event: " . $e->getMessage();
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
