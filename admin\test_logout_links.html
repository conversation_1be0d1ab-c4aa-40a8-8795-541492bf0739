<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test Logout Links</title>
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0"><i class="fa-solid fa-link me-2"></i>Logout Links Test</h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">This page tests all logout links to ensure they point to the correct logout.php file.</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">Sidebar Logout Link</h6>
                                    </div>
                                    <div class="card-body">
                                        <a href="logout.php" class="btn btn-danger w-100">
                                            <i class="fa-solid fa-right-from-bracket me-2"></i>Sidebar Logout
                                        </a>
                                        <small class="text-muted d-block mt-2">Should redirect to logout.php</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">Dropdown Logout Link</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="dropdown">
                                            <button class="btn btn-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                                User Menu
                                            </button>
                                            <ul class="dropdown-menu w-100">
                                                <li><a class="dropdown-item" href="#">Profile</a></li>
                                                <li><a class="dropdown-item" href="#">Settings</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                                            </ul>
                                        </div>
                                        <small class="text-muted d-block mt-2">Dropdown logout should work</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">Direct Links Test</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <a href="logout.php" class="btn btn-outline-danger w-100">
                                            Direct logout.php
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <a href="./logout.php" class="btn btn-outline-danger w-100">
                                            ./logout.php
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <button onclick="testLogoutAjax()" class="btn btn-outline-warning w-100">
                                            AJAX Test
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">Navigation Links</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="index.php" class="btn btn-primary w-100">
                                            Dashboard
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="login.php" class="btn btn-success w-100">
                                            Login
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="verify_logout.php" class="btn btn-info w-100">
                                            Verify Logout
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="test_logout.php" class="btn btn-secondary w-100">
                                            Test Session
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fa-solid fa-info-circle me-2"></i>Expected Behavior:</h6>
                            <ol class="mb-0">
                                <li>Click any logout link</li>
                                <li>Should redirect to login.php</li>
                                <li>Should show "You have been successfully logged out" message</li>
                                <li>Session should be destroyed</li>
                            </ol>
                        </div>

                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./assets/js/bootstrap.bundle.min.js"></script>
    <script>
        function testLogoutAjax() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="alert alert-info">Testing AJAX logout...</div>';
            
            fetch('logout.php', {
                method: 'GET',
                redirect: 'manual'
            })
            .then(response => {
                if (response.type === 'opaqueredirect' || response.status === 0) {
                    resultsDiv.innerHTML = '<div class="alert alert-success">✅ AJAX Test: logout.php redirected correctly</div>';
                } else if (response.redirected) {
                    resultsDiv.innerHTML = '<div class="alert alert-success">✅ AJAX Test: logout.php redirected to ' + response.url + '</div>';
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-warning">⚠️ AJAX Test: Unexpected response - Status: ' + response.status + '</div>';
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = '<div class="alert alert-danger">❌ AJAX Test Error: ' + error.message + '</div>';
            });
        }

        // Test if logout.php file exists
        function checkLogoutFile() {
            fetch('logout.php', { method: 'HEAD' })
            .then(response => {
                const status = response.ok ? 'exists and accessible' : 'may have issues (status: ' + response.status + ')';
                console.log('logout.php file ' + status);
            })
            .catch(error => {
                console.error('Error checking logout.php:', error);
            });
        }

        // Run file check on page load
        document.addEventListener('DOMContentLoaded', checkLogoutFile);
    </script>
</body>
</html>
