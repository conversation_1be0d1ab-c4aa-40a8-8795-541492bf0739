<?php
// Start session for flash messages
session_start();

// Include database connection
require_once 'database/conn.php';

// Create a simple database wrapper class for this file
class SimpleDB {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
}

// Create database instance
$db = new SimpleDB($pdo);

// Fetch all users
try {
    $users = $db->fetchAll("
        SELECT id, name, profile_image 
        FROM users 
        ORDER BY created_at DESC
    ");
} catch (Exception $e) {
    $users = [];
    echo "Error fetching users: " . $e->getMessage();
}

// Helper function to get profile image URL - direct approach
function getProfileImageUrl($image_path, $default_image = './assets/images/profile.png') {
    if (empty($image_path)) {
        return $default_image;
    }
    
    // Just return the path as stored in the database for display and debugging
    return $image_path;
}

// Show all the uploaded profile images for debugging
function listAllProfileImages() {
    $path = './uploads/profiles/';
    if (is_dir($path)) {
        $files = scandir($path);
        $results = [];
        foreach ($files as $file) {
            if ($file != '.' && $file != '..' && !is_dir($path . $file)) {
                $results[] = $file;
            }
        }
        return $results;
    }
    return [];
}

$profile_images = listAllProfileImages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Image Display Test</title>
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .profile-container {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 10px;
        }
        .test-image {
            max-width: 150px;
            max-height: 150px;
            margin: 10px;
            border: 1px solid #ccc;
        }
        .image-path {
            word-break: break-all;
            margin-top: 5px;
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Image Display Test</h1>

        <div class="card mb-5">
            <div class="card-header bg-primary text-white">
                <h2 class="h5 mb-0">Image Storage Location Check</h2>
            </div>
            <div class="card-body">
                <h3>Files in ./uploads/profiles/ directory:</h3>
                <?php if (!empty($profile_images)): ?>
                    <ul class="list-group">
                        <?php foreach ($profile_images as $image): ?>
                            <li class="list-group-item">
                                <div>
                                    <strong><?php echo htmlspecialchars($image); ?></strong>
                                    <img src="./uploads/profiles/<?php echo htmlspecialchars($image); ?>" class="test-image" alt="Test">
                                    <div class="image-path">Path: ./uploads/profiles/<?php echo htmlspecialchars($image); ?></div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <p class="alert alert-warning">No images found in the uploads/profiles directory.</p>
                <?php endif; ?>
            </div>
        </div>

        <div class="card mb-5">
            <div class="card-header bg-success text-white">
                <h2 class="h5 mb-0">User Profile Images from Database</h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if (!empty($users)): ?>
                        <?php foreach ($users as $user): ?>
                            <div class="col-md-4">
                                <div class="profile-container">
                                    <h3><?php echo htmlspecialchars($user['name']); ?></h3>
                                    
                                    <!-- Option 1: Direct Database Path -->
                                    <div class="mb-4">
                                        <h4>Direct Database Path:</h4>
                                        <img src="<?php echo $user['profile_image']; ?>" class="profile-image" alt="Direct">
                                        <div class="image-path"><?php echo htmlspecialchars($user['profile_image']); ?></div>
                                    </div>
                                    
                                    <!-- Option 2: With ./ Prefix -->
                                    <div class="mb-4">
                                        <h4>With ./ Prefix:</h4>
                                        <img src="./<?php echo $user['profile_image']; ?>" class="profile-image" alt="With Prefix">
                                        <div class="image-path">./<?php echo htmlspecialchars($user['profile_image']); ?></div>
                                    </div>
                                    
                                    <!-- Option 3: Using filename only -->
                                    <div class="mb-4">
                                        <h4>Filename Only:</h4>
                                        <?php $filename = basename($user['profile_image']); ?>
                                        <img src="./uploads/profiles/<?php echo $filename; ?>" class="profile-image" alt="Filename Only">
                                        <div class="image-path">./uploads/profiles/<?php echo htmlspecialchars($filename); ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <p class="alert alert-warning">No users found in the database.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <a href="manage-users.php" class="btn btn-primary">Back to Manage Users</a>
    </div>
</body>
</html>
