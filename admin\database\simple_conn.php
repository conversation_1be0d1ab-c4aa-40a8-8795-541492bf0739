<?php
// Simple database connection - what you might be familiar with

$servername = "localhost";
$username = "root";
$password = "";
$dbname = "baraaro";

// Create connection using mysqli
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "Connected successfully to baraaro database";

// Example usage:
// $sql = "SELECT * FROM users";
// $result = $conn->query($sql);
// if ($result->num_rows > 0) {
//     while($row = $result->fetch_assoc()) {
//         echo "ID: " . $row["id"]. " - Name: " . $row["name"]. "<br>";
//     }
// }

?>
