<?php
/**
 * Logout Script - Baraaro E-Learning Admin Dashboard
 *
 * This script handles user logout by:
 * 1. Starting/resuming the session
 * 2. Logging the logout activity (optional)
 * 3. Destroying all session data
 * 4. Clearing session cookies
 * 5. Regenerating session ID for security
 * 6. Redirecting to login page
 */

// Start session to access session data
session_start();

// Debug: Log that logout was accessed (remove this in production)
error_log("Logout.php accessed at " . date('Y-m-d H:i:s'));

// Optional: Log logout activity before destroying session
if (isset($_SESSION['auth_user']) && !empty($_SESSION['auth_user']['id'])) {
    $user_id = $_SESSION['auth_user']['id'];
    $user_name = $_SESSION['auth_user']['name'] ?? 'Unknown';

    // Debug: Log user info
    error_log("Logging out user: ID={$user_id}, Name={$user_name}");

    // Optional: Log to database (uncomment if you want to track logout activity)
    /*
    try {
        require_once 'database/conn.php';
        $stmt = $pdo->prepare("INSERT INTO user_activity_log (user_id, action, description, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([
            $user_id,
            'logout',
            'User logged out',
            $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
        ]);
    } catch (Exception $e) {
        // Log error silently, don't interrupt logout process
        error_log("Logout activity logging failed: " . $e->getMessage());
    }
    */
}

// Unset all session variables
$_SESSION = array();

// Delete the session cookie if it exists
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Regenerate session ID for security (start a new clean session)
session_start();
session_regenerate_id(true);

// Optional: Set a logout success message for the login page
$_SESSION['logout_message'] = 'You have been successfully logged out.';

// Debug: Log before redirect
error_log("Logout complete, redirecting to login.php");

// Redirect to login page
header('Location: login.php');
exit;
?>