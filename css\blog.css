/* Blog Section Padding */
.ftco-section {
    padding: 7em 0;
    position: relative;
}

/* Blog Image Styling */
.block-20 {
    height: 250px;
    display: block;
    background-size: cover;
    background-position: center center;
    position: relative;
    margin-bottom: 0;
    border-radius: 5px 5px 0 0;
    overflow: hidden;
}

@media (min-width: 768px) {
    .blog-entry {
      margin-bottom: 30px; 
    } 
}
  
@media (max-width: 767.98px) {
    .blog-entry {
      margin-bottom: 30px; 
    } 
    .ftco-section {
      padding: 4em 0;
    }
}
  
  .blog-entry .text {
    position: relative;
    border-top: 0;
    border-radius: 2px; }
    .blog-entry .text .heading {
      font-size: 20px;
      margin-bottom: 16px; }
      .blog-entry .text .heading a {
        color: #000000; }
        .blog-entry .text .heading a:hover, .blog-entry .text .heading a:focus, .blog-entry .text .heading a:active {
          color: #fd5f00; }
    .blog-entry .text .meta-chat {
      color: #b3b3b3; }
    .blog-entry .text .read {
      color: #000000;
      font-size: 14px; }
  
  .blog-entry .meta-date {
    display: inline-block;
    background: #1dbf71; /* Change to match the site's primary color */
    border-radius: 4px;
    padding: 8px 12px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: absolute;
    bottom: 15px;
    left: 15px;
  }
    .blog-entry .meta-date span {
      display: block;
      color: #fff; }
    .blog-entry .meta-date .day {
      font-weight: 700;
      font-size: 20px; }
    .blog-entry .meta-date .mos, .blog-entry .meta-date .yr {
      font-size: 13px; }
  
  .blog-entry .meta > div {
    display: block;
    margin-right: 5px;
    margin-bottom: 0px;
    font-size: 15px; }
    .blog-entry .meta > div a {
      color: #b3b3b3;
      font-size: 13px; }
      .blog-entry .meta > div a:hover {
        color: #cccccc; }
  
  