/**
 * Path Fixer Utility
 * 
 * This script fixes relative paths in HTML files to ensure proper loading of CSS, JS, and image files.
 * It can be used to fix paths in any HTML file or directory of HTML files.
 * 
 * Usage:
 * 1. To fix paths in a single file: node fix_paths.js --file=path/to/file.html
 * 2. To fix paths in a directory: node fix_paths.js --dir=path/to/directory
 * 3. To specify a custom path prefix: node fix_paths.js --dir=path/to/directory --prefix=../custom/path/
 */

const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
let targetFile = null;
let targetDir = null;
let pathPrefix = '../assets/';

args.forEach(arg => {
    if (arg.startsWith('--file=')) {
        targetFile = arg.split('=')[1];
    } else if (arg.startsWith('--dir=')) {
        targetDir = arg.split('=')[1];
    } else if (arg.startsWith('--prefix=')) {
        pathPrefix = arg.split('=')[1];
    }
});

/**
 * Fix paths in a single HTML file
 * @param {string} filePath - Path to the HTML file
 * @param {string} prefix - Path prefix to use (e.g., '../assets/')
 */
function fixPathsInFile(filePath, prefix) {
    console.log(`Processing file: ${filePath}`);
    
    try {
        // Read the file content
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Replace relative paths with the specified prefix
        // For CSS files
        content = content.replace(/href="\.\/assets\//g, `href="${prefix}`);
        
        // For JavaScript files
        content = content.replace(/src="\.\/assets\//g, `src="${prefix}`);
        
        // For images
        content = content.replace(/src="\.\/assets\/images\//g, `src="${prefix}images/`);
        
        // Write the updated content back to the file
        fs.writeFileSync(filePath, content, 'utf8');
        
        console.log(`Fixed paths in: ${filePath}`);
        return true;
    } catch (error) {
        console.error(`Error processing file ${filePath}:`, error.message);
        return false;
    }
}

/**
 * Process all HTML files in a directory
 * @param {string} dirPath - Path to the directory
 * @param {string} prefix - Path prefix to use
 */
function fixPathsInDirectory(dirPath, prefix) {
    console.log(`Starting to fix paths in HTML files in directory: ${dirPath}`);
    
    try {
        // Get all files in the directory
        const files = fs.readdirSync(dirPath);
        
        // Process each HTML file
        let filesProcessed = 0;
        let filesSuccessful = 0;
        
        files.forEach(file => {
            if (file.endsWith('.html')) {
                const filePath = path.join(dirPath, file);
                const success = fixPathsInFile(filePath, prefix);
                filesProcessed++;
                if (success) filesSuccessful++;
            }
        });
        
        console.log(`Completed! Fixed paths in ${filesSuccessful}/${filesProcessed} files.`);
    } catch (error) {
        console.error(`Error processing directory ${dirPath}:`, error.message);
    }
}

// Main execution
if (targetFile) {
    // Fix paths in a single file
    fixPathsInFile(targetFile, pathPrefix);
} else if (targetDir) {
    // Fix paths in all HTML files in a directory
    fixPathsInDirectory(targetDir, pathPrefix);
} else {
    console.log(`
Usage:
1. To fix paths in a single file: node fix_paths.js --file=path/to/file.html
2. To fix paths in a directory: node fix_paths.js --dir=path/to/directory
3. To specify a custom path prefix: node fix_paths.js --dir=path/to/directory --prefix=../custom/path/
`);
}
