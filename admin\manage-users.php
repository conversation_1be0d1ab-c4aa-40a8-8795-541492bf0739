<?php
// Start session for flash messages
session_start();

// Check if the user is authenticated
if (!isset($_SESSION['auth_user']) || empty($_SESSION['auth_user']['id'])) {
    // User is not logged in, redirect to login page
    header('Location: login.php');
    exit; // Make sure to exit to prevent further script execution
}

// Include database connection
require_once 'database/conn.php';

// Create a simple database wrapper class for this file
class SimpleDB {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }

    public function update($table, $data, $where, $params = []) {
        $set = implode(', ', array_map(function($key) {
            return "{$key} = :{$key}";
        }, array_keys($data)));

        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);

        // Merge data and where params
        $allParams = array_merge($data, $params);
        return $stmt->execute($allParams);
    }
}

// Create database instance
$db = new SimpleDB($pdo);

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $user_ids = $_POST['user_ids'] ?? [];

    if (!empty($user_ids)) {
        $user_ids_str = implode(',', array_map('intval', $user_ids));

        switch ($action) {
            case 'delete':
                $db->delete('users', "id IN ({$user_ids_str})");
                $_SESSION['success_message'] = "Selected users have been deleted successfully.";
                break;
        }
    }
}

// Handle single user delete
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_user'])) {
    $user_id = (int)$_POST['user_id'];
    $db->delete('users', 'id = ?', [$user_id]);
    $_SESSION['success_message'] = "User has been deleted successfully.";
}

// Fetch all users
try {
    $users = $db->fetchAll("
        SELECT id, name, phone, email, user_role, profile_image, address, created_at
        FROM users
        ORDER BY created_at DESC
    ");
} catch (Exception $e) {
    $users = [];
    $_SESSION['error_message'] = "Error fetching users: " . $e->getMessage();
}

// Helper function to get profile image URL
function getProfileImageUrl($image_path, $default_image = './assets/images/profile.png') {
    // If path is empty, return default image
    if (empty($image_path)) {
        return $default_image;
    }

    // Extract the filename from the path, regardless of directory structure
    $filename = basename($image_path);

    // Always use the uploads/profiles directory with the extracted filename
    // This ensures we find the image regardless of how the path is stored in the database
    $corrected_path = './uploads/profiles/' . $filename;

    // If the file exists in our corrected path, use it
    if (file_exists($corrected_path)) {
        return $corrected_path;
    }

    // If the exact path exists as given, use it
    if (file_exists($image_path)) {
        return $image_path;
    }

    // If all else fails, just return the path as is (for debugging)
    return $corrected_path;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Dashboard | Baraaro Fruit Empire</title>
    <!-- Stylesheets -->
    <link rel="shortcut icon" href="./assets/images/favicon.ico" type="image/x-icon">
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/brands.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
    <link href="./assets/plugin/datatable/datatables.min.css" rel="stylesheet">
    <link href="./assets/css/style.css" rel="stylesheet">
    <style>
        .tbl-img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
            color: #fff;
        }
        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
            color: #fff;
        }
        .btn-primary {
            background-color: #2a1c19;
            border-color: #2a1c19;
        }
        .btn-primary:hover {
            background-color: #1a100d;
            border-color: #1a100d;
        }
        .badge {
            padding: 0.5em 0.8em;
            font-weight: 500;
        }
        .dataTables_wrapper .dataTables_filter input {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin-left: 5px;
        }
        .dataTables_wrapper .dataTables_length select {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin: 0 5px;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #2a1c19;
            color: #fff !important;
            border: 1px solid #2a1c19;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #1a100d;
            color: #fff !important;
            border: 1px solid #1a100d;
        }
    </style>
</head>
<body>
    <!-- Preloader -->
    <div id="preloader">
        <div class="spinner"></div>
    </div>
    <!-- Main Wrapper -->
    <div id="main-wrapper" class="d-flex">
       <!--sidebar here -->
           <?php include 'include/sidebar.php'; ?>
       <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Header -->
            <div class="header d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="collapse-sidebar me-3 d-none d-lg-block text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="menu-toggle me-3 d-block d-lg-none text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="d-none d-md-block d-lg-block">
                        <div class="input-group flex-nowrap">
                            <span class="input-group-text bg-white " id="addon-wrapping"><i class="fa-solid search-icon fa-magnifying-glass text-color-1"></i></span>
                            <input type="text" class="form-control search-input border-l-none ps-0" placeholder="Search anything" aria-label="Username" aria-describedby="addon-wrapping">
                        </div>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <ul class="nav d-flex align-items-center">
                        <!-- Messages Dropdown -->


                         <!-- User Profile -->
                        <li class="nav-item dropdown user-profile">
                            <div class="d-flex align-items-center dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="user-avatar me-0 me-lg-3">
                                    <?php
                                    // Get profile image path and create proper URL
                                    $profile_image_path = isset($_SESSION['auth_user']['profile_image']) ? $_SESSION['auth_user']['profile_image'] : '';
                                    $profile_image_url = '';

                                    if (!empty($profile_image_path)) {
                                        // Extract filename from path regardless of directory structure
                                        $filename = basename($profile_image_path);

                                        // Always use the uploads/profiles directory with the extracted filename
                                        $corrected_path = './uploads/profiles/' . $filename;

                                        // Check if file exists in corrected path
                                        if (file_exists($corrected_path)) {
                                            $profile_image_url = $corrected_path;
                                        }
                                        // If corrected path doesn't work, try the original path
                                        elseif (file_exists($profile_image_path)) {
                                            $profile_image_url = $profile_image_path;
                                        }
                                    }

                                    // Display image if we have a valid URL
                                    if (!empty($profile_image_url)): ?>
                                        <img src="<?php echo htmlspecialchars($profile_image_url); ?>" alt="User" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <!-- Fallback to letter avatar with proper styling -->
                                        <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                                             style="width: 40px; height: 40px; background-color: #6c757d; font-size: 16px;">
                                            <?php
                                            if (isset($_SESSION['auth_user']['name']) && !empty($_SESSION['auth_user']['name'])) {
                                                echo strtoupper(substr(htmlspecialchars($_SESSION['auth_user']['name']), 0, 1));
                                            } else {
                                                echo 'A'; // Default initial
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </span>
                                <div>
                                    <a href="#" class="d-none d-lg-block">
                                        <span class="d-block auth-role">
                                            <?php
                                                if (isset($_SESSION['auth_user']['role'])) {
                                                    echo htmlspecialchars(ucfirst(str_replace('_', ' ', $_SESSION['auth_user']['role'])));
                                                } else {
                                                    echo 'Guest'; // Default role
                                                }
                                            ?>
                                        </span>
                                        <span class="auth-name">
                                            <?php echo isset($_SESSION['auth_user']['name']) ? htmlspecialchars($_SESSION['auth_user']['name']) : 'Guest User'; // Default name ?>
                                        </span>
                                        <span class="ms-2 text-color-1 text-size-sm"><i class="fa-solid fa-angle-down"></i></span>
                                    </a>
                                    <ul class="dropdown-menu mt-3">
                                        <li><a class="dropdown-item" href="#">Profile</a></li>
                                        <li><a class="dropdown-item" href="#">Settings</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                      
                                    </ul>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <!-- Main Content -->
            <div class="main-content">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex align-items-lg-center flex-column flex-md-row flex-lg-row mt-3">
                            <div class="flex-grow-1">
                                <h3 class="mb-2 text-color-2">Manage Users</h3>
                                <p class="text-muted">View, edit, and manage all users in the system</p>
                            </div>
                            <div class="mt-3 mt-md-0">
                                <a href="create-user.php" class="btn bg-primary text-white px-4 py-2">
                                    <i class="fa-solid fa-user-plus me-2"></i> Add New User
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                        <i class="fa-solid fa-check-circle me-2"></i>
                        <?php
                        echo $_SESSION['success_message'];
                        unset($_SESSION['success_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                        <i class="fa-solid fa-exclamation-triangle me-2"></i>
                        <?php
                        echo $_SESSION['error_message'];
                        unset($_SESSION['error_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body">
                                <form id="bulkActionForm" method="post">
                                    <div class="row mb-3">
                                        <div class="col-md-6 d-flex align-items-center">
                                            <div class="bulk-actions">
                                                <select class="form-select" id="bulkActionSelect" name="bulk_action">
                                                    <option value="">Bulk Actions</option>
                                                    <option value="delete">Delete Selected</option>
                                                </select>
                                            </div>
                                            <button type="submit" class="btn btn-sm btn-primary ms-2">Apply</button>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover align-middle" id="usersTable">
                                            <thead class="bg-light">
                                                <tr>
                                                    <th scope="col" class="py-3"><input type="checkbox" id="select-all" class="custom-checkbox"></th>
                                                    <th scope="col" class="py-3">Name</th>
                                                    <th scope="col" class="py-3">Phone</th>
                                                    <th scope="col" class="py-3">Email</th>
                                                    <th scope="col" class="py-3">User Role</th>
                                                    <th scope="col" class="py-3">Created Date</th>
                                                    <th scope="col" class="py-3 text-center">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php if (!empty($users)): ?>
                                                    <?php foreach ($users as $user): ?>
                                                        <tr data-user-id="<?php echo $user['id']; ?>">
                                                            <td>
                                                                <input type="checkbox" class="user-checkbox custom-checkbox" name="user_ids[]" value="<?php echo $user['id']; ?>">
                                                            </td>
                                                            <td>
                                                                <div class="d-flex align-items-center">
                                                                    <img src="<?php echo getProfileImageUrl($user['profile_image']); ?>" class="tbl-img me-3" alt="Profile">
                                                                    <span><?php echo htmlspecialchars($user['name']); ?></span>
                                                                </div>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($user['phone']); ?></td>
                                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                            <td>
                                                                <span class="badge bg-<?php echo $user['user_role'] === 'admin' ? 'primary' : 'secondary'; ?>">
                                                                    <?php echo htmlspecialchars(ucfirst($user['user_role'])); ?>
                                                                </span>
                                                            </td>
                                                            <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                                            <td class="text-center">
                                                                <a href="#" data-bs-toggle="modal" data-bs-target="#viewUserModal"
                                                                   data-id="<?php echo $user['id']; ?>"
                                                                   data-name="<?php echo htmlspecialchars($user['name']); ?>"
                                                                   data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                                   data-phone="<?php echo htmlspecialchars($user['phone']); ?>"
                                                                   data-role="<?php echo htmlspecialchars($user['user_role']); ?>"
                                                                   data-image="<?php echo htmlspecialchars($user['profile_image']); ?>"
                                                                   data-address="<?php echo htmlspecialchars($user['address'] ?? ''); ?>"
                                                                   data-created="<?php echo htmlspecialchars($user['created_at']); ?>"
                                                                   class="btn btn-sm btn-info me-2 view-user">
                                                                    <i class="fa-regular fa-eye"></i>
                                                                </a>
                                                                <a href="#" data-bs-toggle="modal" data-bs-target="#editUserModal"
                                                                   data-id="<?php echo $user['id']; ?>"
                                                                   data-name="<?php echo htmlspecialchars($user['name']); ?>"
                                                                   data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                                   data-phone="<?php echo htmlspecialchars($user['phone']); ?>"
                                                                   data-role="<?php echo htmlspecialchars($user['user_role']); ?>"
                                                                   data-image="<?php echo htmlspecialchars($user['profile_image']); ?>"
                                                                   data-address="<?php echo htmlspecialchars($user['address'] ?? ''); ?>"
                                                                   data-created="<?php echo htmlspecialchars($user['created_at']); ?>"
                                                                   class="btn btn-sm btn-primary me-2 edit-user">
                                                                    <i class="fa-regular fa-pen-to-square"></i>
                                                                </a>
                                                                <a href="#" data-bs-toggle="modal" data-bs-target="#deleteUserModal"
                                                                   data-user-id="<?php echo $user['id']; ?>"
                                                                   data-user-name="<?php echo htmlspecialchars($user['name']); ?>"
                                                                   class="btn btn-sm btn-danger delete-user">
                                                                    <i class="fa-solid fa-trash-can"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <tr>
                                                        <td colspan="7" class="text-center py-4">
                                                            <div class="text-muted">
                                                                <i class="fa-solid fa-users fa-2x mb-3"></i>
                                                                <p>No users found in the system.</p>
                                                                <a href="create-user.php" class="btn btn-primary btn-sm">
                                                                    <i class="fa-solid fa-user-plus me-2"></i>Add New User
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- View User Modal -->
                <div class="modal fade" id="viewUserModal" tabindex="-1" aria-labelledby="viewUserModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="viewUserModalLabel">User Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <img id="viewUserImage" src="./assets/images/profile.png" class="rounded-circle" width="100" height="100" alt="User Image" style="object-fit: cover;">
                                    <h5 class="mt-3 mb-1" id="viewUserName"></h5>
                                    <span id="viewUserRole" class="badge"></span>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Email:</label>
                                        <p id="viewUserEmail"></p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Phone:</label>
                                        <p id="viewUserPhone"></p>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label fw-bold">Address:</label>
                                        <p id="viewUserAddress"></p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Created:</label>
                                        <p id="viewUserCreated"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Edit User Modal -->
                <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editUserForm" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="user_id" id="edit_user_id">
                                    <!-- Name Field -->
                                    <div class="col-md-6">
                                        <label for="edit_name" class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="edit_name" name="name" required>
                                    </div>

                                    <!-- Phone Field -->
                                    <div class="col-md-6">
                                        <label for="edit_phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="edit_phone" name="phone" required>
                                    </div>

                                    <!-- Email Field -->
                                    <div class="col-md-6">
                                        <label for="edit_email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="edit_email" name="email" required>
                                    </div>

                                    <!-- User Role Field -->
                                    <div class="col-md-6">
                                        <label for="edit_user_role" class="form-label">User Role</label>
                                        <select class="form-select" id="edit_user_role" name="user_role" required>
                                            <option value="admin">Admin</option>
                                            <option value="super_admin">Super Admin</option>
                                            <option value="author">Author</option>
                                            <option value="editor">Editor</option>
                                            <option value="subscriber">Subscriber</option>
                                            <option value="student">Student</option>
                                        </select>
                                    </div>

                                    <!-- Address Field -->
                                    <div class="col-12">
                                        <label for="edit_address" class="form-label">Address</label>
                                        <textarea class="form-control" id="edit_address" name="address" rows="3"></textarea>
                                    </div>

                                    <!-- Profile Image Field -->
                                    <div class="col-12">
                                        <label for="edit_profile_image" class="form-label">Profile Image</label>
                                        <input type="file" class="form-control" id="edit_profile_image" name="profile_image">
                                        <div class="mt-2">
                                            <img id="current_profile_image" src="" width="50" height="50" class="rounded-circle" alt="Current Image">
                                            <small class="ms-2 text-muted">Current image</small>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" form="editUserForm" class="btn btn-primary">Save Changes</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete User Modal -->
                <div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="deleteUserModalLabel">Confirm Delete</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>Are you sure you want to delete <span id="deleteUserName" class="fw-bold"></span>? This action cannot be undone.</p>
                                <div class="alert alert-danger">
                                    <div class="d-flex align-items-center">
                                        <i class="fa-solid fa-triangle-exclamation me-2"></i>
                                        <div>
                                            <strong>Warning:</strong> All data associated with this user will be permanently deleted.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="user_id" id="delete_user_id">
                                    <input type="hidden" name="delete_user" value="1">
                                    <button type="submit" class="btn btn-danger">Delete User</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
             <!-- Footer -->
             <div class="footer text-center bg-white shadow-sm py-3 mt-5">
                <p class="m-0">Copyright © 2025. All Rights Reserved. <a href="https://finalskills.com" class="text-primary" target="_blank" >Developed by final skills</a></p>
            </div>
    </div>
     <!-- Scripts -->
    <script  src="./assets/js/jquery-3.6.0.min.js"></script>
    <script  src="./assets/js/bootstrap.bundle.min.js"></script>
    <script  src="./assets/plugin/chart/chart.js"></script>
    <script src="./assets/plugin/datatable/datatables.min.js"></script>
    <script  src="./assets/js/chart.js"></script>
    <script  src="./assets/js/main.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize DataTable with pagination
            const userTable = $('#usersTable').DataTable({
                responsive: true,
                order: [[1, 'asc']], // Sort by name column by default
                columnDefs: [
                    { orderable: false, targets: [0, 5, 6] } // Disable sorting for checkbox, image and actions columns
                ],
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Search users...",
                    lengthMenu: "Show _MENU_ users per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ users",
                    infoEmpty: "No users found",
                    infoFiltered: "(filtered from _MAX_ total users)"
                },
                // Enable pagination
                paging: true,
                pagingType: "full_numbers",
                pageLength: 10,
                lengthMenu: [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]]
            });

            // Handle select all checkbox
            $('#select-all').on('change', function() {
                $('.user-checkbox').prop('checked', $(this).prop('checked'));
            });

            // Update select all checkbox state based on row checkboxes
            $(document).on('change', '.user-checkbox', function() {
                const totalCheckboxes = $('.user-checkbox').length;
                const checkedCheckboxes = $('.user-checkbox:checked').length;
                $('#select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
            });

            // Handle view user modal - use delegation for dynamic content
            $(document).on('click', '.view-user', function(e) {
                e.preventDefault();

                // Get data directly from data attributes
                const $this = $(this);
                const userData = {
                    id: $this.data('id'),
                    name: $this.data('name'),
                    email: $this.data('email'),
                    phone: $this.data('phone'),
                    user_role: $this.data('role'),
                    profile_image: $this.data('image'),
                    address: $this.data('address'),
                    created_at: $this.data('created')
                };

                console.log('View user data:', userData); // Debug info
                console.log('Address field:', userData.address); // Specifically log address

                // Set the profile image with fallback
                const imagePath = userData.profile_image ? ('./uploads/profiles/' + userData.profile_image.split('/').pop()) : './assets/images/profile.png';
                $('#viewUserImage').attr('src', imagePath);

                // Set user name
                $('#viewUserName').text(userData.name || 'Unknown');

                // Set user role with appropriate badge
                const role = userData.user_role || 'user';
                $('#viewUserRole')
                    .text(role.charAt(0).toUpperCase() + role.slice(1))
                    .removeClass()
                    .addClass('badge')
                    .addClass(getRoleBadgeClass(role));

                // Set other user details with explicit fallbacks
                $('#viewUserEmail').text(userData.email || 'Not provided');
                $('#viewUserPhone').text(userData.phone || 'Not provided');

                // Handle address specially - check if it exists and is not null/empty
                if (userData.address && userData.address.trim() !== '') {
                    $('#viewUserAddress').text(userData.address);
                } else {
                    $('#viewUserAddress').text('No address provided');
                }

                // Format the created date
                try {
                    const createdDate = userData.created_at ? new Date(userData.created_at).toLocaleString() : 'Unknown';
                    $('#viewUserCreated').text(createdDate);
                } catch (error) {
                    $('#viewUserCreated').text(userData.created_at || 'Unknown');
                }

                // Show the modal
                $('#viewUserModal').modal('show');
            });

            // Handle edit user modal - use delegation for dynamic content
            $(document).on('click', '.edit-user', function(e) {
                e.preventDefault();

                // Get data directly from data attributes
                const $this = $(this);
                const userData = {
                    id: $this.data('id'),
                    name: $this.data('name'),
                    email: $this.data('email'),
                    phone: $this.data('phone'),
                    user_role: $this.data('role'),
                    profile_image: $this.data('image'),
                    address: $this.data('address'),
                    created_at: $this.data('created')
                };

                console.log('Edit user data:', userData); // Debug info

                // Populate the edit form
                $('#edit_user_id').val(userData.id);
                $('#edit_name').val(userData.name);
                $('#edit_phone').val(userData.phone);
                $('#edit_email').val(userData.email);
                $('#edit_user_role').val(userData.user_role);
                $('#edit_address').val(userData.address || '');

                // Set profile image
                const imagePath = userData.profile_image ? ('./uploads/profiles/' + userData.profile_image.split('/').pop()) : './assets/images/profile.png';
                $('#current_profile_image').attr('src', imagePath);

                // Show the modal
                $('#editUserModal').modal('show');
            });

            // Handle delete user modal - use delegation for dynamic content
            $(document).on('click', '.delete-user', function(e) {
                e.preventDefault();
                const userId = $(this).data('user-id');
                const userName = $(this).data('user-name');
                $('#delete_user_id').val(userId);
                $('#deleteUserName').text(userName);
                $('#deleteUserModal').modal('show');
            });

            // Handle bulk actions
            $('#bulkActionForm').on('submit', function(e) {
                const action = $('#bulkActionSelect').val();
                const checkedUsers = $('.user-checkbox:checked').length;

                if (action === '' || checkedUsers === 0) {
                    e.preventDefault();
                    alert('Please select an action and at least one user.');
                    return false;
                }

                if (action === 'delete' && !confirm('Are you sure you want to delete ' + checkedUsers + ' selected user(s)? This action cannot be undone.')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Helper function to get role badge class
            function getRoleBadgeClass(role) {
                switch(role) {
                    case 'super_admin': return 'bg-danger';
                    case 'admin': return 'bg-primary';
                    case 'author': return 'bg-success';
                    case 'editor': return 'bg-info';
                    case 'student': return 'bg-warning text-dark';
                    default: return 'bg-secondary';
                }
            }

            // Helper function to get profile image URL
            function getProfileImageUrl(imagePath) {
                return imagePath && imagePath !== 'null' ? imagePath : './assets/images/profile.png';
            }

            // Handle edit user form submission
            $('#editUserForm').on('submit', function(e) {
                e.preventDefault();

                // Show loading indicator
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fa-solid fa-spinner fa-spin me-2"></i> Saving...');
                submitBtn.prop('disabled', true);

                const formData = new FormData(this);

                $.ajax({
                    url: 'update-user.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        let data;

                        try {
                            // Handle case where response is already parsed
                            data = (typeof response === 'object') ? response : JSON.parse(response);
                        } catch (e) {
                            console.error('Error parsing JSON response:', e);
                            data = { success: false, message: 'Invalid server response' };
                        }

                        if (data.success) {
                            // Show success message before reload
                            const alertHtml = `
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fa-solid fa-check-circle me-2"></i>
                                    ${data.message}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            `;

                            // Close modal
                            $('#editUserModal').modal('hide');

                            // Show message and reload after a short delay
                            $('<div class="mt-3"></div>').html(alertHtml).insertAfter('.d-flex.align-items-lg-center');
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            // Show error message
                            const errorHtml = `
                                <div class="alert alert-danger mt-3 mb-0">
                                    <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                    ${data.message || 'Error updating user'}
                                </div>
                            `;

                            // Replace any existing error message
                            $('#editUserForm .alert').remove();
                            $(errorHtml).prependTo('#editUserForm');

                            // Reset button
                            submitBtn.html(originalText);
                            submitBtn.prop('disabled', false);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);

                        // Show error message
                        const errorHtml = `
                            <div class="alert alert-danger mt-3 mb-0">
                                <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                Error updating user: ${error || 'Unknown error'}
                            </div>
                        `;

                        // Replace any existing error message
                        $('#editUserForm .alert').remove();
                        $(errorHtml).prependTo('#editUserForm');

                        // Reset button
                        submitBtn.html(originalText);
                        submitBtn.prop('disabled', false);
                    }
                });
            });
        });
    </script>
</body>
</html>