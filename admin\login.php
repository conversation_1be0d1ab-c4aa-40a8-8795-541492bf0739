<?php
// Start session
session_start();

// If user is already logged in, redirect to dashboard
if (isset($_SESSION['auth_user']) && !empty($_SESSION['auth_user']['id'])) {
    header('Location: index.php');
    exit;
}

// Include database connection
require_once 'database/conn.php';

// Re-define SimpleDB class or include it from a shared file if you have one
// For simplicity, defining it here if not already defined.
if (!class_exists('SimpleDB')) {
    class SimpleDB {
        private $pdo;

        public function __construct($pdo) {
            $this->pdo = $pdo;
        }

        public function fetchRow($sql, $params = []) {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        }
    }
}

$db = new SimpleDB($pdo);

$error_message = '';
$form_email = ''; // To repopulate email field on error

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $form_email = filter_var(trim($_POST['email'] ?? ''), FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'] ?? '';

    if (empty($form_email) || !filter_var($form_email, FILTER_VALIDATE_EMAIL)) {
        $error_message = "Valid email is required.";
    } elseif (empty($password)) {
        $error_message = "Password is required.";
    } else {
        // Fetch user by email
        // Ensure your users table has columns: id, name, email, user_role, password, profile_image, phone, address
        $user = $db->fetchRow("SELECT * FROM users WHERE email = ?", [$form_email]);

        if ($user && password_verify($password, $user['password'])) {
            // Password is correct, set session variables
            $_SESSION['auth_user'] = [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['user_role'], // Ensure this column name matches your DB
                'profile_image' => $user['profile_image'],
                'phone' => $user['phone'],
                'address' => $user['address']
            ];

            // Redirect to dashboard or a protected page
            header('Location: index.php');
            exit;
        } else {
            // Keep $form_email populated for the input field
            $error_message = "Invalid email or password.";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Login</title>
    <!-- Stylesheets -->
    <link rel="shortcut icon" href="./assets/images/favicon.ico" type="image/x-icon">
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/brands.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
    <link href="./assets/plugin/quill/quill.snow.css" rel="stylesheet">
    <link href="./assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center min-vh-100 align-items-center">
            <div class="col-11 col-sm-8 col-md-8 col-lg-4">
                <div class="bg-white rounded-4 shadow-sm p-4">
                    <!-- Logo -->
                    <div class="text-center mb-4">
                        <div class="d-flex align-items-center justify-content-center gap-2">
                            <a href="login.php"><img src="./assets/images/logo.png" alt="logo"></a>
                        </div>
                    </div>

                    <!-- Sign In Form -->
                    <h2 class="mb-4 text-dark h4">Sign In</h2>

                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fa-solid fa-exclamation-triangle me-2"></i>
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['logout_message'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fa-solid fa-check-circle me-2"></i>
                            <?php echo $_SESSION['logout_message']; unset($_SESSION['logout_message']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="login.php">
                        <!-- Email Input -->
                        <div class="mb-3 position-relative">
                            <label for="email" class="form-label text-muted small">Email</label>
                            <div class="position-relative">
                                <input type="email" class="form-control form-control-lg rounded-3" name="email"
                                       id="email" placeholder="<EMAIL>" value="<?php echo htmlspecialchars($form_email); ?>" required>
                                <i class="fas fa-envelope input-icon"></i>
                            </div>
                        </div>

                        <!-- Password Input -->
                        <div class="mb-4 position-relative">
                            <label for="password" class="form-label text-muted small">Password</label>
                            <div class="position-relative">
                                <input type="password" class="form-control form-control-lg rounded-3" name="password"
                                       id="password" placeholder="••••••••" required>
                                <i class="fas fa-lock input-icon"></i>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12 col-lg-6 mb-2 mb-lg-0">
                            <div class="form-check ps-0">
                                <input type="checkbox" class="custom-checkbox" id="flexCheckDefault" name="remember_me">
                                <label class="form-check-label" for="flexCheckDefault">Remember me</label>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 text-lg-end">
                            <a href="forgot-password.html" class="text-primary">Forgot password</a>
                            </div>
                        </div>


                        <!-- Sign In Button -->
                        <button type="submit" class="btn btn-signin btn-lg w-100 rounded-3 mb-4">
                            Sign In
                        </button>

                        <!-- Divider -->

                    </form>
                </div>
            </div>
        </div>
    </div>

    <script  src="./assets/js/jquery-3.6.0.min.js"></script>
    <script  src="./assets/js/bootstrap.bundle.min.js"></script>
    <script  src="./assets/plugin/chart/chart.js"></script>
    <script  src="./assets/plugin/quill/quill.js"></script>
    <script  src="./assets/js/chart.js"></script>
    <script  src="./assets/js/main.js"></script>
</body>
</html>
