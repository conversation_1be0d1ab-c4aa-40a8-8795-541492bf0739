<?php
// Start session
session_start();

// Check if the user is authenticated
if (!isset($_SESSION['auth_user']) || empty($_SESSION['auth_user']['id'])) {
    // User is not logged in, redirect to login page
    header('Location: login.php');
    exit; // Make sure to exit to prevent further script execution
}

// Include database connection
require_once 'database/conn.php';

// Create a simple database wrapper class for this file
class SimpleDB {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    public function fetchRow($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }

    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($data);

        return $this->pdo->lastInsertId();
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }

    public function update($table, $data, $where, $params = []) {
        $set = implode(', ', array_map(function($key) {
            return "{$key} = :{$key}";
        }, array_keys($data)));

        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);

        // Merge data and where params
        $allParams = array_merge($data, $params);
        return $stmt->execute($allParams);
    }
}

// Create database instance
$db = new SimpleDB($pdo);

// Initialize variables
$success_message = '';
$error_message = '';
$form_data = [];

// Handle edit form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_slider'])) {
    try {
        $slider_id = (int)$_POST['slider_id'];

        // Validate and sanitize input data
        $subtitle = trim(htmlspecialchars($_POST['subtitle'] ?? '', ENT_QUOTES, 'UTF-8'));
        $title = trim(htmlspecialchars($_POST['title'] ?? '', ENT_QUOTES, 'UTF-8'));
        $description = trim(htmlspecialchars($_POST['description'] ?? '', ENT_QUOTES, 'UTF-8'));

        // Validation
        $errors = [];
        if (empty($subtitle)) $errors[] = "Subtitle is required";
        if (empty($title)) $errors[] = "Title is required";
        if (empty($description)) $errors[] = "Description is required";

        // Get current slider data
        $current_slider = $db->fetchRow("SELECT * FROM sliders WHERE id = ?", [$slider_id]);
        if (!$current_slider) {
            $errors[] = "Slider not found";
        }

        $image_path = $current_slider['image']; // Keep current image by default

        // Handle file upload if new image is provided
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/sliders/';

            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            // Validate file type
            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = "Only JPG, JPEG, PNG, GIF, and WebP files are allowed";
            }
            // Validate file size (5MB limit)
            elseif ($_FILES['image']['size'] > 5 * 1024 * 1024) {
                $errors[] = "File size must be less than 5MB";
            }
            // Validate actual image file (security check)
            elseif (!getimagesize($_FILES['image']['tmp_name'])) {
                $errors[] = "Invalid image file";
            }
            else {
                // Generate unique filename to prevent conflicts
                $unique_filename = uniqid('slider_', true) . '.' . $file_extension;
                $upload_path = $upload_dir . $unique_filename;

                // Move uploaded file to destination
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    // Delete old image file if it exists
                    if ($current_slider['image'] && file_exists($current_slider['image'])) {
                        unlink($current_slider['image']);
                    }
                    $image_path = $upload_path;
                } else {
                    $errors[] = "Failed to upload slider image";
                }
            }
        }

        // If no errors, update slider
        if (empty($errors)) {
            $slider_data = [
                'subtitle' => $subtitle,
                'title' => $title,
                'description' => $description,
                'image' => $image_path
            ];

            if ($db->update('sliders', $slider_data, 'id = ?', ['id' => $slider_id])) {
                $success_message = "Slider updated successfully!";
            } else {
                $error_message = "Failed to update slider. Please try again.";
            }
        } else {
            $error_message = implode('<br>', $errors);
        }

    } catch (Exception $e) {
        error_log("Slider update error: " . $e->getMessage());
        $error_message = "An error occurred while updating the slider. Please try again.";
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['edit_slider'])) {
    try {
        // Validate and sanitize input data
        $subtitle = trim(htmlspecialchars($_POST['subtitle'] ?? '', ENT_QUOTES, 'UTF-8'));
        $title = trim(htmlspecialchars($_POST['title'] ?? '', ENT_QUOTES, 'UTF-8'));
        $description = trim(htmlspecialchars($_POST['description'] ?? '', ENT_QUOTES, 'UTF-8'));

        // Store form data for repopulation on error
        $form_data = [
            'subtitle' => $subtitle,
            'title' => $title,
            'description' => $description
        ];

        // Validation
        $errors = [];

        if (empty($subtitle)) $errors[] = "Subtitle is required";
        if (empty($title)) $errors[] = "Title is required";
        if (empty($description)) $errors[] = "Description is required";

        // Handle file upload - Store only file path in database
        $image_path = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/sliders/';

            // Create upload directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            // Validate file type
            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = "Only JPG, JPEG, PNG, GIF, and WebP files are allowed";
            }
            // Validate file size (5MB limit)
            elseif ($_FILES['image']['size'] > 5 * 1024 * 1024) {
                $errors[] = "File size must be less than 5MB";
            }
            // Validate actual image file (security check)
            elseif (!getimagesize($_FILES['image']['tmp_name'])) {
                $errors[] = "Invalid image file";
            }
            else {
                // Generate unique filename to prevent conflicts
                $unique_filename = uniqid('slider_', true) . '.' . $file_extension;
                $upload_path = $upload_dir . $unique_filename;

                // Move uploaded file to destination
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    // Store only the relative path in database
                    $image_path = $upload_path;
                } else {
                    $errors[] = "Failed to upload slider image";
                }
            }
        } else {
            $errors[] = "Slider image is required";
        }

        // If no errors, insert slider
        if (empty($errors)) {
            $slider_data = [
                'subtitle' => $subtitle,
                'title' => $title,
                'description' => $description,
                'image' => $image_path
            ];

            $slider_id = $db->insert('sliders', $slider_data);

            if ($slider_id) {
                $success_message = "Slider created successfully! Slider ID: " . $slider_id;
                $form_data = []; // Clear form data on success
            } else {
                $error_message = "Failed to create slider. Please try again.";
            }
        } else {
            $error_message = implode('<br>', $errors);
        }

    } catch (Exception $e) {
        error_log("Slider creation error: " . $e->getMessage());
        $error_message = "An error occurred while creating the slider. Please try again.";
    }
}

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    try {
        $slider_id = (int)$_GET['id'];

        // Get slider image path before deleting
        $slider = $db->fetchRow("SELECT image FROM sliders WHERE id = ?", [$slider_id]);

        if ($slider && $db->delete('sliders', 'id = ?', [$slider_id])) {
            // Delete the image file if it exists
            if ($slider['image'] && file_exists($slider['image'])) {
                unlink($slider['image']);
            }
            $success_message = "Slider deleted successfully!";
        } else {
            $error_message = "Failed to delete slider.";
        }
    } catch (Exception $e) {
        error_log("Slider deletion error: " . $e->getMessage());
        $error_message = "An error occurred while deleting the slider.";
    }
}

// Fetch all sliders for display
try {
    $sliders = $db->fetchAll("SELECT * FROM sliders ORDER BY created_at DESC");
} catch (Exception $e) {
    $sliders = [];
    error_log("Error fetching sliders: " . $e->getMessage());
}

?>
<?php include 'include/header.php'; ?>
    <!-- Preloader -->

    <!-- Main Wrapper -->
    <div id="main-wrapper" class="d-flex">
        <?php include 'include/sidebar.php'; ?>
       <!-- Content Wrapper -->
        <div class="content-wrapper">
            <?php include 'include/top-navbar.php'; ?>

            <!-- Main Content -->
          <div class="main-content">
              <div class="row">
                  <div class="col-12">
                      <div class="d-flex align-items-lg-center flex-column flex-md-row flex-lg-row mt-3">
                          <div class="flex-grow-1">
                              <h3 class="mb-2 text-color-2">Slider Management</h3>
                              <p class="text-color-3 mb-0">Create and manage homepage sliders</p>
                          </div>
                      </div>
                  </div>
              </div>

              <!-- Slider Creation Form -->
              <div class="row mt-4">
                  <div class="col-12">
                      <div class="card shadow-sm border-0">
                          <div class="card-header bg-white py-3">
                              <h5 class="card-title mb-0 text-color-2">
                                  <i class="fas fa-plus-circle me-2"></i>Add New Slider
                              </h5>
                          </div>
                          <div class="card-body p-4">
                              <?php if (!empty($success_message)): ?>
                                  <div class="alert alert-success alert-dismissible fade show" role="alert">
                                      <i class="fa-solid fa-check-circle me-2"></i>
                                      <?php echo $success_message; ?>
                                      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                  </div>
                              <?php endif; ?>

                              <?php if (!empty($error_message)): ?>
                                  <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                      <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                      <?php echo $error_message; ?>
                                      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                  </div>
                              <?php endif; ?>

                              <form action="" method="post" enctype="multipart/form-data" class="row g-4">
                                  <!-- Subtitle Field -->
                                  <div class="col-md-6">
                                      <label for="subtitle" class="form-label text-color-2 fw-medium">Subtitle <span class="text-danger">*</span></label>
                                      <input type="text" class="form-control" id="subtitle" name="subtitle" placeholder="Enter slider subtitle" value="<?php echo htmlspecialchars($form_data['subtitle'] ?? '', ENT_QUOTES); ?>" required>
                                  </div>

                                  <!-- Title Field -->
                                  <div class="col-md-6">
                                      <label for="title" class="form-label text-color-2 fw-medium">Title <span class="text-danger">*</span></label>
                                      <input type="text" class="form-control" id="title" name="title" placeholder="Enter slider title" value="<?php echo htmlspecialchars($form_data['title'] ?? '', ENT_QUOTES); ?>" required>
                                  </div>

                                  <!-- Description Field -->
                                  <div class="col-12">
                                      <label for="description" class="form-label text-color-2 fw-medium">Description <span class="text-danger">*</span></label>
                                      <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter slider description" required><?php echo htmlspecialchars($form_data['description'] ?? '', ENT_QUOTES); ?></textarea>
                                  </div>

                                  <!-- Image Field -->
                                  <div class="col-12">
                                      <label for="image" class="form-label text-color-2 fw-medium">Slider Image <span class="text-danger">*</span></label>
                                      <input type="file" class="form-control" id="image" name="image" accept="image/*" required>
                                      <small class="text-muted">
                                          <i class="fa-solid fa-info-circle me-1"></i>
                                          Upload a slider image.
                                          <strong>Supported formats:</strong> JPG, JPEG, PNG, GIF, WebP.
                                          <strong>Max size:</strong> 5MB.
                                          <strong>Recommended size:</strong> 1920x800px for best results.
                                      </small>
                                  </div>

                                  <!-- Submit Button -->
                                  <div class="col-12 mt-4">
                                      <button type="submit" class="btn bg-primary text-white px-4 py-2">
                                          <i class="fa-solid fa-plus me-2"></i> Create Slider
                                      </button>
                                      <button type="reset" class="btn btn-light border ms-2 px-4 py-2">
                                          <i class="fa-solid fa-rotate me-2"></i> Reset
                                      </button>
                                  </div>
                              </form>
                          </div>
                      </div>
                  </div>
              </div>

              <!-- Sliders Table -->
              <div class="row mt-4">
                  <div class="col-12">
                      <div class="card shadow-sm border-0">
                          <div class="card-header bg-white py-3">
                              <h5 class="card-title mb-0 text-color-2">
                                  <i class="fas fa-list me-2"></i>All Sliders
                                  <span class="badge bg-primary ms-2"><?php echo count($sliders); ?></span>
                              </h5>
                          </div>
                          <div class="card-body p-0">
                              <?php if (empty($sliders)): ?>
                                  <div class="text-center py-5">
                                      <i class="fas fa-images text-muted" style="font-size: 3rem;"></i>
                                      <h5 class="text-muted mt-3">No sliders found</h5>
                                      <p class="text-muted">Create your first slider using the form above.</p>
                                  </div>
                              <?php else: ?>
                                  <div class="table-responsive">
                                      <table class="table table-hover align-middle mb-0">
                                          <thead class="table-light">
                                              <tr>
                                                  <th class="px-3 py-3" style="width: 60px;">ID</th>
                                                  <th class="px-3 py-3" style="width: 100px;">Image</th>
                                                  <th class="px-3 py-3" style="width: 150px;">Subtitle</th>
                                                  <th class="px-3 py-3" style="width: 150px;">Title</th>
                                                  <th class="px-3 py-3" style="width: 200px;">Description</th>
                                                  <th class="px-3 py-3" style="width: 120px;">Created</th>
                                                  <th class="px-3 py-3" style="width: 120px;">Updated</th>
                                                  <th class="px-3 py-3 text-center" style="width: 120px;">Actions</th>
                                              </tr>
                                          </thead>
                                          <tbody>
                                              <?php foreach ($sliders as $slider): ?>
                                                  <tr>
                                                      <td class="px-4 py-3">
                                                          <span class="fw-medium text-color-2">#<?php echo $slider['id']; ?></span>
                                                      </td>
                                                      <td class="px-4 py-3">
                                                          <?php if ($slider['image'] && file_exists($slider['image'])): ?>
                                                              <img src="<?php echo htmlspecialchars($slider['image']); ?>"
                                                                   alt="Slider Image"
                                                                   class="rounded"
                                                                   style="width: 80px; height: 50px; object-fit: cover;"
                                                                   data-bs-toggle="tooltip"
                                                                   title="<?php echo htmlspecialchars($slider['title']); ?>">
                                                          <?php else: ?>
                                                              <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                                   style="width: 80px; height: 50px;">
                                                                  <i class="fas fa-image text-muted"></i>
                                                              </div>
                                                          <?php endif; ?>
                                                      </td>
                                                      <td class="px-4 py-3">
                                                          <span class="text-color-2"><?php echo htmlspecialchars($slider['subtitle']); ?></span>
                                                      </td>
                                                      <td class="px-4 py-3">
                                                          <span class="fw-medium text-color-2"><?php echo htmlspecialchars($slider['title']); ?></span>
                                                      </td>
                                                      <td class="px-4 py-3">
                                                          <span class="text-color-3">
                                                              <?php
                                                              $description = htmlspecialchars($slider['description']);
                                                              echo strlen($description) > 100 ? substr($description, 0, 100) . '...' : $description;
                                                              ?>
                                                          </span>
                                                      </td>
                                                      <td class="px-4 py-3">
                                                          <small class="text-color-3">
                                                              <?php echo date('M j, Y', strtotime($slider['created_at'])); ?><br>
                                                              <?php echo date('g:i A', strtotime($slider['created_at'])); ?>
                                                          </small>
                                                      </td>
                                                      <td class="px-4 py-3">
                                                          <small class="text-color-3">
                                                              <?php echo date('M j, Y', strtotime($slider['updated_at'])); ?><br>
                                                              <?php echo date('g:i A', strtotime($slider['updated_at'])); ?>
                                                          </small>
                                                      </td>
                                                      <td class="px-4 py-3 text-center">
                                                          <div class="btn-group" role="group">
                                                              <button type="button"
                                                                      class="btn btn-sm btn-outline-primary"
                                                                      data-bs-toggle="modal"
                                                                      data-bs-target="#viewModal<?php echo $slider['id']; ?>"
                                                                      title="View Details">
                                                                  <i class="fas fa-eye"></i>
                                                              </button>
                                                              <button type="button"
                                                                      class="btn btn-sm btn-outline-success"
                                                                      data-bs-toggle="modal"
                                                                      data-bs-target="#editModal<?php echo $slider['id']; ?>"
                                                                      title="Edit Slider">
                                                                  <i class="fas fa-edit"></i>
                                                              </button>
                                                              <a href="?action=delete&id=<?php echo $slider['id']; ?>"
                                                                 class="btn btn-sm btn-outline-danger"
                                                                 onclick="return confirm('Are you sure you want to delete this slider? This action cannot be undone.')"
                                                                 title="Delete Slider">
                                                                  <i class="fas fa-trash"></i>
                                                              </a>
                                                          </div>
                                                      </td>
                                                  </tr>
                                              <?php endforeach; ?>
                                          </tbody>
                                      </table>
                                  </div>
                              <?php endif; ?>
                          </div>
                      </div>
                  </div>
              </div>

              <!-- View Modals for each slider -->
              <?php foreach ($sliders as $slider): ?>
                  <div class="modal fade" id="viewModal<?php echo $slider['id']; ?>" tabindex="-1" aria-hidden="true">
                      <div class="modal-dialog modal-lg">
                          <div class="modal-content">
                              <div class="modal-header">
                                  <h5 class="modal-title">Slider Details - #<?php echo $slider['id']; ?></h5>
                                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                              </div>
                              <div class="modal-body">
                                  <div class="row">
                                      <div class="col-md-6">
                                          <?php if ($slider['image'] && file_exists($slider['image'])): ?>
                                              <img src="<?php echo htmlspecialchars($slider['image']); ?>"
                                                   alt="Slider Image"
                                                   class="img-fluid rounded">
                                          <?php else: ?>
                                              <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                   style="height: 200px;">
                                                  <i class="fas fa-image text-muted" style="font-size: 3rem;"></i>
                                              </div>
                                          <?php endif; ?>
                                      </div>
                                      <div class="col-md-6">
                                          <h6 class="text-color-2">Subtitle:</h6>
                                          <p class="text-color-3"><?php echo htmlspecialchars($slider['subtitle']); ?></p>

                                          <h6 class="text-color-2">Title:</h6>
                                          <p class="text-color-3"><?php echo htmlspecialchars($slider['title']); ?></p>

                                          <h6 class="text-color-2">Description:</h6>
                                          <p class="text-color-3"><?php echo nl2br(htmlspecialchars($slider['description'])); ?></p>

                                          <h6 class="text-color-2">Image Path:</h6>
                                          <p class="text-color-3 small"><?php echo htmlspecialchars($slider['image']); ?></p>

                                          <h6 class="text-color-2">Created:</h6>
                                          <p class="text-color-3 small"><?php echo date('F j, Y g:i A', strtotime($slider['created_at'])); ?></p>

                                          <h6 class="text-color-2">Last Updated:</h6>
                                          <p class="text-color-3 small"><?php echo date('F j, Y g:i A', strtotime($slider['updated_at'])); ?></p>
                                      </div>
                                  </div>
                              </div>
                              <div class="modal-footer">
                                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                              </div>
                          </div>
                      </div>
                  </div>
              <?php endforeach; ?>

              <!-- Edit Modals for each slider -->
              <?php foreach ($sliders as $slider): ?>
                  <div class="modal fade" id="editModal<?php echo $slider['id']; ?>" tabindex="-1" aria-hidden="true">
                      <div class="modal-dialog modal-lg">
                          <div class="modal-content">
                              <div class="modal-header">
                                  <h5 class="modal-title">Edit Slider - #<?php echo $slider['id']; ?></h5>
                                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                              </div>
                              <form action="" method="post" enctype="multipart/form-data">
                                  <div class="modal-body">
                                      <input type="hidden" name="slider_id" value="<?php echo $slider['id']; ?>">
                                      <input type="hidden" name="edit_slider" value="1">

                                      <div class="row g-3">
                                          <!-- Subtitle Field -->
                                          <div class="col-md-6">
                                              <label for="edit_subtitle_<?php echo $slider['id']; ?>" class="form-label text-color-2 fw-medium">Subtitle <span class="text-danger">*</span></label>
                                              <input type="text" class="form-control" id="edit_subtitle_<?php echo $slider['id']; ?>" name="subtitle" value="<?php echo htmlspecialchars($slider['subtitle']); ?>" required>
                                          </div>

                                          <!-- Title Field -->
                                          <div class="col-md-6">
                                              <label for="edit_title_<?php echo $slider['id']; ?>" class="form-label text-color-2 fw-medium">Title <span class="text-danger">*</span></label>
                                              <input type="text" class="form-control" id="edit_title_<?php echo $slider['id']; ?>" name="title" value="<?php echo htmlspecialchars($slider['title']); ?>" required>
                                          </div>

                                          <!-- Description Field -->
                                          <div class="col-12">
                                              <label for="edit_description_<?php echo $slider['id']; ?>" class="form-label text-color-2 fw-medium">Description <span class="text-danger">*</span></label>
                                              <textarea class="form-control" id="edit_description_<?php echo $slider['id']; ?>" name="description" rows="4" required><?php echo htmlspecialchars($slider['description']); ?></textarea>
                                          </div>

                                          <!-- Current Image Display -->
                                          <div class="col-12">
                                              <label class="form-label text-color-2 fw-medium">Current Image</label>
                                              <div class="mb-3">
                                                  <?php if ($slider['image'] && file_exists($slider['image'])): ?>
                                                      <img src="<?php echo htmlspecialchars($slider['image']); ?>"
                                                           alt="Current Slider Image"
                                                           class="img-thumbnail"
                                                           style="max-width: 200px; max-height: 120px;">
                                                  <?php else: ?>
                                                      <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                           style="width: 200px; height: 120px;">
                                                          <i class="fas fa-image text-muted" style="font-size: 2rem;"></i>
                                                      </div>
                                                  <?php endif; ?>
                                              </div>
                                          </div>

                                          <!-- New Image Field -->
                                          <div class="col-12">
                                              <label for="edit_image_<?php echo $slider['id']; ?>" class="form-label text-color-2 fw-medium">New Image (Optional)</label>
                                              <input type="file" class="form-control" id="edit_image_<?php echo $slider['id']; ?>" name="image" accept="image/*">
                                              <small class="text-muted">
                                                  <i class="fa-solid fa-info-circle me-1"></i>
                                                  Leave empty to keep current image.
                                                  <strong>Supported formats:</strong> JPG, JPEG, PNG, GIF, WebP.
                                                  <strong>Max size:</strong> 5MB.
                                              </small>
                                          </div>
                                      </div>
                                  </div>
                                  <div class="modal-footer">
                                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                      <button type="submit" class="btn bg-primary text-white">
                                          <i class="fas fa-save me-2"></i>Update Slider
                                      </button>
                                  </div>
                              </form>
                          </div>
                      </div>
                  </div>
              <?php endforeach; ?>
          </div>
        </div>
    </div>
<?php include 'include/footer.php'; ?>
