/*** Service Start ***/
.service .service-item {
    border-radius: 10px;
}
.service .service-item .service-img {
    position: relative;
    overflow: hidden;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.service .service-item .service-img img {
    transition: 0.5s;
}

.service .service-item:hover .service-img img {
    transform: scale(1.1);
}

.service .service-item .service-img::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background: rgba(1, 95, 201, .2);
    transition: 0.5s;
    z-index: 1;
}

.service .service-item:hover .service-img::after {
    height: 100%;
}

.service .service-item .service-img .service-icon {
    position: absolute;
    width: 70px;
    bottom: 0;
    right: 25px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background: var(--bs-light);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.5s;
    z-index: 9;
}

.service .service-item .service-img .service-icon i {
    color: var(--bs-primary);
    transition: 0.5s;
}

.service .service-item:hover .service-img .service-icon i {
    transform: rotateX(360deg);
    color: var(--bs-white);
}

.service .service-item:hover .service-img .service-icon {
    bottom: 0;
    color: var(--bs-white);
    background: var(--bs-primary);
}

.service .service-content {
    position: relative;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: var(--bs-light);
}

.service .service-item .service-content .service-content-inner {
    position: relative;
    z-index: 9;
}

.service .service-item .service-content .service-content-inner .h4,
.service .service-item .service-content .service-content-inner p {
    transition: 0.5s;
}

.service .service-item:hover .service-content .service-content-inner .h4,
.service .service-item:hover .service-content .service-content-inner p {
    color: var(--bs-white);
}

.service .service-item:hover .service-content .service-content-inner .btn.btn-primary {
    color: var(--bs-primary);
    background: var(--bs-white);
}

.service .service-item:hover .service-content .service-content-inner .btn.btn-primary:hover {
    color: var(--bs-white);
    background: var(--bs-dark);
}

.service .service-item:hover .service-content .service-content-inner .h4:hover {
    color: var(--bs-dark);
}

.service .service-item .service-content::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 0;
    bottom: 0;
    left: 0;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: var(--bs-primary);
    transition: 0.5s;
    z-index: 1;
}

.service .service-item:hover .service-content::after {
    height: 100%;
}
/*** Service End ***/
