<?php
// Start session for flash messages
session_start();

// Include database connection
require_once 'database/conn.php';

// Create a simple database wrapper class for this file
class SimpleDB {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function fetchRow($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }

    public function update($table, $data, $where, $params = []) {
        $set = implode(', ', array_map(function($key) {
            return "{$key} = :{$key}";
        }, array_keys($data)));
        
        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);
        
        // Merge data and where params
        $allParams = array_merge($data, $params);
        return $stmt->execute($allParams);
    }
}

// Create database instance
$db = new SimpleDB($pdo);

// Initialize response array
$response = [
    'success' => false,
    'message' => 'No action taken'
];

// Handle slider update via AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['slider_id'])) {
    try {
        // Get slider ID and validate it's an integer
        $slider_id = (int)$_POST['slider_id'];
        
        // Check if slider exists
        $existing_slider = $db->fetchRow("SELECT id, image FROM sliders WHERE id = ?", [$slider_id]);
        if (!$existing_slider) {
            $response['message'] = "Slider not found";
            echo json_encode($response);
            exit;
        }
        
        // Validate and sanitize input data
        $subtitle = trim(htmlspecialchars($_POST['subtitle'] ?? '', ENT_QUOTES, 'UTF-8'));
        $title = trim(htmlspecialchars($_POST['title'] ?? '', ENT_QUOTES, 'UTF-8'));
        $description = trim(htmlspecialchars($_POST['description'] ?? '', ENT_QUOTES, 'UTF-8'));
        
        // Validation
        $errors = [];

        if (empty($subtitle)) $errors[] = "Subtitle is required";
        if (empty($title)) $errors[] = "Title is required";
        if (empty($description)) $errors[] = "Description is required";
        
        // Handle file upload if a new image is provided
        $image_path = $existing_slider['image']; // Keep existing by default
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/sliders/';

            // Create upload directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            // Validate file type
            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = "Only JPG, JPEG, PNG, GIF, and WebP files are allowed";
            }
            // Validate file size (5MB limit)
            elseif ($_FILES['image']['size'] > 5 * 1024 * 1024) {
                $errors[] = "File size must be less than 5MB";
            }
            // Validate actual image file (security check)
            elseif (!getimagesize($_FILES['image']['tmp_name'])) {
                $errors[] = "Invalid image file";
            }
            else {
                // Generate unique filename to prevent conflicts
                $unique_filename = uniqid('slider_', true) . '.' . $file_extension;
                $upload_path = $upload_dir . $unique_filename;

                // Move uploaded file to destination
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    // Delete old image file if it exists and is different
                    if ($existing_slider['image'] && $existing_slider['image'] !== $upload_path && file_exists($existing_slider['image'])) {
                        unlink($existing_slider['image']);
                    }
                    // Store only the relative path in database
                    $image_path = $upload_path;
                } else {
                    $errors[] = "Failed to upload slider image";
                }
            }
        }
        
        // If no errors, update slider
        if (empty($errors)) {
            $slider_data = [
                'subtitle' => $subtitle,
                'title' => $title,
                'description' => $description,
                'image' => $image_path
            ];
            
            $success = $db->update('sliders', $slider_data, 'id = :id', ['id' => $slider_id]);
            
            if ($success) {
                $response['success'] = true;
                $response['message'] = "Slider updated successfully";
                
                // Store success message in session for page reload
                $_SESSION['success_message'] = "Slider has been updated successfully.";
            } else {
                $response['message'] = "Failed to update slider. Please try again.";
            }
        } else {
            $response['message'] = implode("<br>", $errors);
        }
        
    } catch (Exception $e) {
        error_log("Slider update error: " . $e->getMessage());
        $response['message'] = "An error occurred while updating the slider: " . $e->getMessage();
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
