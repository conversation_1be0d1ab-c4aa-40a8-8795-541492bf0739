                <div class="row">
                    <div class="col-12">
                        <div class="d-flex align-items-lg-center flex-column flex-md-row flex-lg-row mt-3">
                            <div class="flex-grow-1">
                                <h3 class="mb-2 text-color-2">Create New User</h3>
                                <p class="text-muted">Add a new user to the system with appropriate role and permissions</p>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                        <i class="fa-solid fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                        <i class="fa-solid fa-exclamation-triangle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body">
                                <form method="post" enctype="multipart/form-data">
                                    <div class="row">
                                        <!-- Name Field -->
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo htmlspecialchars($form_data['name'] ?? ''); ?>" required>
                                        </div>

                                        <!-- Phone Field -->
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo htmlspecialchars($form_data['phone'] ?? ''); ?>" required>
                                        </div>

                                        <!-- Email Field -->
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($form_data['email'] ?? ''); ?>" required>
                                        </div>

                                        <!-- User Role Field -->
                                        <div class="col-md-6 mb-3">
                                            <label for="user_role" class="form-label">User Role <span class="text-danger">*</span></label>
                                            <select class="form-select" id="user_role" name="user_role" required>
                                                <option value="">Select Role</option>
                                                <option value="admin" <?php echo ($form_data['user_role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                                <option value="super_admin" <?php echo ($form_data['user_role'] ?? '') === 'super_admin' ? 'selected' : ''; ?>>Super Admin</option>
                                                <option value="author" <?php echo ($form_data['user_role'] ?? '') === 'author' ? 'selected' : ''; ?>>Author</option>
                                                <option value="editor" <?php echo ($form_data['user_role'] ?? '') === 'editor' ? 'selected' : ''; ?>>Editor</option>
                                                <option value="subscriber" <?php echo ($form_data['user_role'] ?? '') === 'subscriber' ? 'selected' : ''; ?>>Subscriber</option>
                                                <option value="student" <?php echo ($form_data['user_role'] ?? '') === 'student' ? 'selected' : ''; ?>>Student</option>
                                            </select>
                                        </div>

                                        <!-- Password Field -->
                                        <div class="col-md-6 mb-3">
                                            <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                            <input type="password" class="form-control" id="password" name="password" required>
                                            <div class="form-text">Password must be at least 8 characters long</div>
                                        </div>

                                        <!-- Confirm Password Field -->
                                        <div class="col-md-6 mb-3">
                                            <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        </div>

                                        <!-- Address Field -->
                                        <div class="col-12 mb-3">
                                            <label for="address" class="form-label">Address</label>
                                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($form_data['address'] ?? ''); ?></textarea>
                                        </div>

                                        <!-- Profile Image Field -->
                                        <div class="col-12 mb-3">
                                            <label for="profile_image" class="form-label">Profile Image</label>
                                            <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                                            <div class="form-text">Supported formats: JPG, JPEG, PNG, GIF, WebP. Max size: 2MB</div>
                                        </div>

                                        <!-- Submit Button -->
                                        <div class="col-12">
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fa-solid fa-user-plus me-2"></i>Create User
                                                </button>
                                                <a href="manage-users.php" class="btn btn-secondary">
                                                    <i class="fa-solid fa-arrow-left me-2"></i>Back to Users
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
