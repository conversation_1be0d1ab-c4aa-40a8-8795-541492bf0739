<?php
// Include database connection
require_once 'database/conn.php';
require_once 'helpers/image_helper.php';

// Create database instance
class SimpleDB {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
}

$db = new SimpleDB($pdo);

// Fetch users with profile images
try {
    $users = $db->fetchAll("SELECT id, name, email, profile_image, created_at FROM users ORDER BY id DESC");
} catch (Exception $e) {
    $users = [];
    $error = "Error fetching users: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test Profile Images | Baraaro E-Learning</title>
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
    <style>
        .profile-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .profile-img-large {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 50%;
            border: 3px solid #007bff;
            margin-bottom: 15px;
        }
        .image-path {
            font-size: 12px;
            color: #666;
            word-break: break-all;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .no-image {
            background: #f8f9fa;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
    </style>
</head>
<body style="background: #f5f5f5;">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fa-solid fa-images me-2"></i>Profile Images Test</h2>
                    <div>
                        <a href="create-user.php" class="btn btn-primary">
                            <i class="fa-solid fa-user-plus me-2"></i>Create User
                        </a>
                        <a href="database/setup_simple.php" class="btn btn-info">
                            <i class="fa-solid fa-database me-2"></i>Database Setup
                        </a>
                    </div>
                </div>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fa-solid fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fa-solid fa-users me-2"></i>
                            Users and Their Profile Images
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-5">
                                <i class="fa-solid fa-user-slash fa-3x text-muted mb-3"></i>
                                <h5>No users found</h5>
                                <p class="text-muted">Create some users to see their profile images here.</p>
                                <a href="create-user.php" class="btn btn-primary">Create First User</a>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($users as $user): ?>
                                    <div class="col-md-4 col-lg-3">
                                        <div class="profile-card">
                                            <!-- Display profile image -->
                                            <?php if (!empty($user['profile_image']) && file_exists($user['profile_image'])): ?>
                                                <img src="<?php echo htmlspecialchars($user['profile_image']); ?>" 
                                                     alt="<?php echo htmlspecialchars($user['name']); ?>" 
                                                     class="profile-img-large">
                                                <div class="image-path">
                                                    <strong>Path:</strong><br>
                                                    <?php echo htmlspecialchars($user['profile_image']); ?>
                                                </div>
                                                <div class="mt-2">
                                                    <small class="text-success">
                                                        <i class="fa-solid fa-check-circle"></i> 
                                                        File exists (<?php echo getFileSize($user['profile_image']); ?>)
                                                    </small>
                                                </div>
                                            <?php elseif (!empty($user['profile_image'])): ?>
                                                <div class="profile-img-large no-image">
                                                    <div>
                                                        <i class="fa-solid fa-image-slash fa-2x"></i><br>
                                                        <small>File Missing</small>
                                                    </div>
                                                </div>
                                                <div class="image-path">
                                                    <strong>Path (Missing):</strong><br>
                                                    <?php echo htmlspecialchars($user['profile_image']); ?>
                                                </div>
                                                <div class="mt-2">
                                                    <small class="text-danger">
                                                        <i class="fa-solid fa-exclamation-triangle"></i> 
                                                        File not found
                                                    </small>
                                                </div>
                                            <?php else: ?>
                                                <div class="profile-img-large no-image">
                                                    <div>
                                                        <i class="fa-solid fa-user fa-2x"></i><br>
                                                        <small>No Image</small>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <small class="text-muted">
                                                        <i class="fa-solid fa-info-circle"></i> 
                                                        No profile image uploaded
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <!-- User info -->
                                            <h6 class="mt-3 mb-1"><?php echo htmlspecialchars($user['name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                            <br>
                                            <small class="text-muted">ID: <?php echo $user['id']; ?></small>
                                            <br>
                                            <small class="text-muted">
                                                Created: <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                            </small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Upload Directory Info -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fa-solid fa-folder me-2"></i>
                            Upload Directory Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Upload Directory:</strong> <code>uploads/profiles/</code><br>
                                <strong>Directory Exists:</strong> 
                                <?php if (is_dir('uploads/profiles/')): ?>
                                    <span class="text-success"><i class="fa-solid fa-check"></i> Yes</span>
                                <?php else: ?>
                                    <span class="text-danger"><i class="fa-solid fa-times"></i> No</span>
                                <?php endif; ?>
                                <br>
                                <strong>Directory Writable:</strong> 
                                <?php if (is_writable('uploads/profiles/')): ?>
                                    <span class="text-success"><i class="fa-solid fa-check"></i> Yes</span>
                                <?php else: ?>
                                    <span class="text-danger"><i class="fa-solid fa-times"></i> No</span>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <strong>Files in Upload Directory:</strong><br>
                                <?php
                                if (is_dir('uploads/profiles/')) {
                                    $files = glob('uploads/profiles/*');
                                    if (empty($files)) {
                                        echo '<em class="text-muted">No files uploaded yet</em>';
                                    } else {
                                        foreach ($files as $file) {
                                            if (is_file($file)) {
                                                echo '<small>' . basename($file) . ' (' . getFileSize($file) . ')</small><br>';
                                            }
                                        }
                                    }
                                } else {
                                    echo '<em class="text-muted">Directory does not exist</em>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="./assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>
