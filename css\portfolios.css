/* Portfolios Page Styles */

/* Hero Section */
.portfolio-hero {
    position: relative;
    overflow: hidden;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('../img/agriculture-field.jpg');
    background-size: cover;
    background-position: center;
    padding: 150px 0;
    color: #fff;
}

.portfolio-hero h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.portfolio-hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto 30px;
    text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
}

/* Portfolio Filter Buttons */
.portfolio-filter {
    margin-bottom: 30px;
}

.portfolio-filter button {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
    margin: 0 5px 10px;
    padding: 8px 20px;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    outline: none;
}

.portfolio-filter button:hover,
.portfolio-filter button.active {
    background: var(--primary);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(34, 139, 34, 0.2);
}

/* Portfolio Items */
.portfolio-item {
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.5s ease;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.portfolio-img {
    position: relative;
    overflow: hidden;
}

.portfolio-img img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.portfolio-item:hover .portfolio-img img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(34, 139, 34, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.5s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-overlay-content {
    text-align: center;
    padding: 20px;
    color: #fff;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.5s ease 0.1s;
}

.portfolio-item:hover .portfolio-overlay-content {
    transform: translateY(0);
    opacity: 1;
}

.portfolio-overlay-content h4 {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 10px;
}

.portfolio-overlay-content p {
    font-size: 14px;
    margin-bottom: 15px;
}

.portfolio-details {
    padding: 20px;
    background: #fff;
    transition: all 0.3s ease;
}

.portfolio-details h5 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-details h5 {
    color: var(--primary);
}

.portfolio-details .categories {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
}

.portfolio-details .category {
    background: rgba(34, 139, 34, 0.1);
    color: var(--primary);
    font-size: 12px;
    font-weight: 600;
    padding: 3px 10px;
    border-radius: 20px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.portfolio-details .date {
    color: #777;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.portfolio-details .date i {
    margin-right: 5px;
    color: var(--primary);
}

/* Featured Portfolio Section */
.featured-portfolio {
    padding: 50px 0;
    background: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), url('../img/pattern.png');
    background-size: cover;
}

.featured-portfolio-item {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    height: 100%;
    transition: all 0.5s ease;
}

.featured-portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.featured-portfolio-img {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.featured-portfolio-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.featured-portfolio-item:hover .featured-portfolio-img img {
    transform: scale(1.1);
}

.featured-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--primary);
    color: #fff;
    font-size: 12px;
    font-weight: 700;
    padding: 5px 15px;
    border-radius: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.featured-portfolio-content {
    padding: 25px;
}

.featured-portfolio-content h4 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.featured-portfolio-item:hover .featured-portfolio-content h4 {
    color: var(--primary);
}

.featured-portfolio-content p {
    color: #777;
    margin-bottom: 20px;
}

.featured-portfolio-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.featured-portfolio-client {
    display: flex;
    align-items: center;
}

.featured-portfolio-client img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
}

.featured-portfolio-client-info h6 {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 2px;
}

.featured-portfolio-client-info span {
    font-size: 12px;
    color: #777;
}

/* Client Testimonials */
.testimonial-item {
    padding: 30px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin: 15px;
    transition: all 0.3s ease;
}

.testimonial-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.testimonial-client {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.testimonial-client img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
    border: 3px solid var(--primary);
}

.testimonial-client-info h5 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 5px;
}

.testimonial-client-info span {
    font-size: 14px;
    color: #777;
}

.testimonial-text {
    position: relative;
    padding-left: 20px;
    border-left: 3px solid var(--primary);
    font-style: italic;
    color: #555;
}

.testimonial-text p {
    margin-bottom: 15px;
}

.testimonial-rating {
    color: #FFD700;
    font-size: 18px;
}

/* Stats Section */
.portfolio-stats {
    position: relative;
    background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('../img/farm-landscape.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    padding: 80px 0;
    color: #fff;
}

.stat-item {
    text-align: center;
    padding: 30px 15px;
}

.stat-icon {
    font-size: 36px;
    color: var(--primary);
    margin-bottom: 15px;
}

.stat-number {
    font-size: 42px;
    font-weight: 800;
    margin-bottom: 5px;
}

.stat-text {
    font-size: 16px;
    font-weight: 600;
    opacity: 0.8;
}

/* CTA Section */
.portfolio-cta {
    background: linear-gradient(90deg, var(--primary) 0%, #1dbf71 100%);
    padding: 60px 0;
    color: #fff;
    text-align: center;
}

.portfolio-cta h2 {
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 20px;
}

.portfolio-cta p {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto 30px;
}

.portfolio-cta .btn {
    background: #fff;
    color: var(--primary);
    border: 2px solid #fff;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 700;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.portfolio-cta .btn:hover {
    background: transparent;
    color: #fff;
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* Animation Enhancements */
.fadeInUp, .fadeIn, .slideInUp {
    animation-duration: 1.2s;
}

/* Gallery Modal Styles */
.portfolio-modal .modal-dialog {
    max-width: 800px;
}

.portfolio-modal .modal-content {
    border-radius: 10px;
    overflow: hidden;
    border: none;
}

.portfolio-modal .modal-header {
    border: none;
    padding: 20px 30px 0;
}

.portfolio-modal .modal-title {
    font-size: 24px;
    font-weight: 700;
}

.portfolio-modal .modal-body {
    padding: 20px 30px 30px;
}

.portfolio-modal-img {
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 20px;
}

.portfolio-modal-img img {
    width: 100%;
    border-radius: 5px;
}

.portfolio-modal-info h5 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--primary);
}

.portfolio-modal-info p {
    margin-bottom: 20px;
}

.portfolio-modal-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.portfolio-modal-meta-item {
    display: flex;
    align-items: flex-start;
}

.portfolio-modal-meta-item i {
    color: var(--primary);
    margin-right: 10px;
    font-size: 16px;
    padding-top: 3px;
}

.portfolio-modal-meta-item div {
    flex: 1;
}

.portfolio-modal-meta-item h6 {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 5px;
}

.portfolio-modal-meta-item p {
    font-size: 14px;
    color: #777;
    margin: 0;
}

/* Responsive styles */
@media (max-width: 992px) {
    .portfolio-filter button {
        margin-bottom: 10px;
    }
    
    .featured-portfolio-img {
        height: 220px;
    }
}

@media (max-width: 768px) {
    .portfolio-hero h1 {
        font-size: 2.5rem;
    }
    
    .portfolio-stats {
        padding: 60px 0;
    }
    
    .stat-item {
        margin-bottom: 30px;
    }
    
    .stat-number {
        font-size: 36px;
    }
}

@media (max-width: 576px) {
    .portfolio-hero h1 {
        font-size: 2rem;
    }
    
    .portfolio-hero p {
        font-size: 1rem;
    }
    
    .portfolio-modal .modal-dialog {
        margin: 10px;
    }
    
    .portfolio-modal-meta {
        grid-template-columns: 1fr;
    }
}
