// Function to fix paths in admin HTML files
const fs = require('fs');
const path = require('path');

// Directory containing admin HTML files
const adminDir = path.join(__dirname, 'admin');

// Function to fix paths in a single HTML file
function fixPathsInFile(filePath) {
    console.log(`Processing file: ${filePath}`);

    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');

    // Replace relative paths with absolute paths
    // For CSS files
    content = content.replace(/href="\.\/assets\//g, 'href="../assets/');

    // For JavaScript files
    content = content.replace(/src="\.\/assets\//g, 'src="../assets/');

    // For images
    content = content.replace(/src="\.\/assets\/images\//g, 'src="../assets/images/');

    // Write the updated content back to the file
    fs.writeFileSync(filePath, content, 'utf8');

    console.log(`Fixed paths in: ${filePath}`);
}

// Process all HTML files in the admin directory
function fixAllAdminFiles() {
    console.log('Starting to fix paths in admin HTML files...');

    // Get all HTML files in the admin directory
    const files = fs.readdirSync(adminDir);

    // Process each HTML file
    let filesProcessed = 0;

    files.forEach(file => {
        if (file.endsWith('.html')) {
            const filePath = path.join(adminDir, file);
            fixPathsInFile(filePath);
            filesProcessed++;
        }
    });

    console.log(`Completed! Fixed paths in ${filesProcessed} files.`);
}

// Run the function
fixAllAdminFiles();
